// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    apply from: './versions.gradle'
    ext.readium_version = '2.4.1'
    ext {
        kotlin_version = '1.7.10'
    }

    addRepos(repositories) //增加代码仓库
    dependencies {
        classpath deps.android_gradle_plugin
        classpath deps.android_maven_gradle_plugin

        classpath 'com.chenenyu:img-optimizer:1.2.0' // 图片压缩
        //美团多渠道打包
        classpath 'com.meituan.android.walle:plugin:1.1.6'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        //滴滴的质量优化框架
        if (isNeedPackage.toBoolean() && isUseBooster.toBoolean()) {
            classpath deps.booster.gradle_plugin
            classpath deps.booster.task_processed_res
            classpath deps.booster.task_resource_deredundancy
        }
    }
    repositories {
        mavenCentral()
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        google()
    }
}

allprojects {
    addRepos(repositories)
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

repositories {
    google()
    mavenCentral()
}

