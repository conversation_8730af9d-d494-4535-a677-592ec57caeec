<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1848]"><node index="1" text="" resource-id="" class="android.widget.FrameLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1848]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1848]"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1848]"><node index="0" text="" resource-id="cn.geyuantz.reader:id/action_bar_root" class="android.widget.LinearLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1848]"><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1848]"><node index="0" text="" resource-id="cn.geyuantz.reader:id/fragment_container" class="android.widget.FrameLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1848]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1848]"><node index="0" text="" resource-id="cn.geyuantz.reader:id/toolbar_container" class="android.widget.FrameLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,120]"><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,120]"><node NAF="true" index="0" text="" resource-id="" class="android.widget.TextView" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][156,120]" /><node index="1" text="" resource-id="" class="android.view.View" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,117][1080,120]" /><node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[156,0][924,120]"><node index="0" text="AI党史" resource-id="" class="android.widget.TextView" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="true" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[461,23][619,96]" /></node><node index="3" text="" resource-id="" class="android.widget.LinearLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[996,0][1080,120]" /></node></node><node index="1" text="" resource-id="cn.geyuantz.reader:id/content_container" class="android.widget.FrameLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,120][1080,1848]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,120][1080,1848]"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,120][1080,1687]"><node index="0" text="" resource-id="cn.geyuantz.reader:id/msg_list" class="androidx.recyclerview.widget.RecyclerView" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,120][1080,1687]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,120][1080,670]"><node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,150][1080,646]"><node index="0" text="" resource-id="cn.geyuantz.reader:id/aurora_iv_msgitem_avatar" class="android.widget.ImageView" package="cn.geyuantz.reader" content-desc="用户头像" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[24,150][150,276]" /><node index="1" text="好的，我已明晰任务。请问你想了解关于党史的哪个方面的知识呢？例如：党史的重要事件、重要人物、党的发展历程等。请提供更具体的信息，以便我更准确地为你提供帮助。" resource-id="cn.geyuantz.reader:id/aurora_tv_msgitem_message" class="android.widget.TextView" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[162,150][918,646]" /></node></node></node></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1687][1080,1848]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,1735][876,1848]"><node index="0" text=" " resource-id="cn.geyuantz.reader:id/et_message_input" class="android.widget.EditText" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[96,1771][840,1836]" /></node><node index="1" text="" resource-id="cn.geyuantz.reader:id/fab_send" class="android.widget.ImageButton" package="cn.geyuantz.reader" content-desc="" checkable="false" checked="false" clickable="true" enabled="false" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[912,1752][1032,1848]" /></node></node></node></node></node></node></node></node></node></node></node></hierarchy>