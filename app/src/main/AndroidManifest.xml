<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="cn.geyuantz.reader" >
    <!-- 进程杀死 -->
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>

    <application
        android:name="cn.geyuantz.reader.MyApp"
        android:allowBackup="false"
        android:configChanges="screenSize|keyboardHidden|orientation|keyboard"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:windowSoftInputMode="adjustPan|stateHidden"
        tools:ignore="LockedOrientationActivity"
        tools:replace="android:allowBackup" >
        <activity
            android:name="cn.geyuantz.reader.activity.SplashActivity"
            android:configChanges="screenSize|keyboardHidden|orientation|keyboard"
            android:screenOrientation="unspecified"
            android:theme="@style/AppTheme.Launch.App"
            android:windowSoftInputMode="adjustPan|stateHidden"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="cn.geyuantz.reader.activity.MainActivity"
            android:configChanges="screenSize|keyboardHidden|orientation|keyboard"
            android:screenOrientation="unspecified"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="cn.geyuantz.reader.activity.MainActivity1"
            android:configChanges="screenSize|keyboardHidden|orientation|keyboard"
            android:screenOrientation="unspecified"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="cn.geyuantz.reader.activity.LoginActivity"
            android:configChanges="screenSize|keyboardHidden|orientation|keyboard"

            android:launchMode="singleInstance"
            android:screenOrientation="unspecified"
            android:windowSoftInputMode="adjustPan|stateHidden" /> <!-- 通用浏览器 -->
        <activity
            android:name="cn.geyuantz.reader.core.webview.AgentWebActivity"
            android:configChanges="screenSize|keyboardHidden|orientation|keyboard"
            android:screenOrientation="unspecified"
            android:hardwareAccelerated="true"
            android:label="@string/app_browser_name"
            android:theme="@style/AppTheme" >

            <!-- Scheme -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="com.xuexiang.xui.applink" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:scheme="about" />
                <data android:scheme="javascript" />
                <!-- 设置自己的deeplink -->
                <!-- <data -->
                <!-- android:host="xxx.com" -->
                <!-- android:scheme="xui"/> -->
            </intent-filter>
            <!-- AppLink -->
            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:scheme="inline" />
                <data android:mimeType="text/html" />
                <data android:mimeType="text/plain" />
                <data android:mimeType="application/xhtml+xml" />
                <data android:mimeType="application/vnd.wap.xhtml+xml" />
                <!-- 设置自己的applink -->
                <!-- <data -->
                <!-- android:host="xxx.com" -->
                <!-- android:scheme="http"/> -->
                <!-- <data -->
                <!-- android:host="xxx.com" -->
                <!-- android:scheme="https"/> -->
            </intent-filter>
        </activity> <!-- fragment的页面容器 -->
        <activity
            android:name="cn.geyuantz.reader.core.BaseActivity"
            android:configChanges="screenSize|keyboardHidden|orientation|keyboard"
            android:screenOrientation="unspecified"
            android:windowSoftInputMode="adjustPan|stateHidden" /> <!-- 版本更新提示 -->
        <activity
            android:name="cn.geyuantz.reader.utils.update.UpdateTipDialog"
            android:screenOrientation="unspecified"
            android:theme="@style/DialogTheme" /> <!-- Webview拦截提示弹窗 -->
        <activity
            android:name="cn.geyuantz.reader.core.webview.WebViewInterceptDialog"
            android:screenOrientation="unspecified"
            android:theme="@style/DialogTheme" /> <!-- applink的中转页面 -->
        <activity
            android:name="cn.geyuantz.reader.core.XPageTransferActivity"
            android:configChanges="screenSize|keyboardHidden|orientation|keyboard"
            android:screenOrientation="unspecified"
            android:windowSoftInputMode="adjustPan|stateHidden" /> <!-- 屏幕自适应设计图 -->
    </application>

</manifest>