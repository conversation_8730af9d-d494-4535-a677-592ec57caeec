<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="cn.geyuantz.reader.fragment.book.BookInfoFragment">

    <!--二维码-->
    <ImageView
        android:id="@+id/iv_qr_code"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_gravity="center"
        android:layout_marginTop="50dp"
        android:src="@mipmap/ic_launcher"
        />
    <!--提示扫码-->
    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:text="请扫描二维码借阅"
        android:textSize="16sp"
        android:textColor="@android:color/black"
        />
    <!--在线阅读按钮-->
    <Button
        android:id="@+id/btn_read_online"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:text="在线阅读"
        android:textSize="16sp"
        android:textColor="@android:color/white"
        android:background="@color/colorPrimary"
        />

</LinearLayout>