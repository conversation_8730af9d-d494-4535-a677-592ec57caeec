<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2019 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center_horizontal"
        app:srcCompat="@mipmap/ic_launcher"
        />


    <TextView
        style="@style/TextStyle.Title"
        android:layout_width="match_parent"
        android:layout_marginTop="10dp"
        android:text="设备注册"
        android:textSize="20sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="24dp"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start|center_vertical"
                android:tint="?attr/colorAccent"
                app:srcCompat="@drawable/ic_menu_privacy" />

            <com.xuexiang.xui.widget.edittext.materialedittext.MaterialEditText
                android:id="@+id/et_device_order"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="36dp"
                android:hint="@string/tip_please_input_phone_number"
                />

        </FrameLayout>


        <FrameLayout
            android:id="@+id/fl_verify_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start|center_vertical"
                android:tint="?attr/colorAccent"
                app:srcCompat="@drawable/ic_comment" />

            <com.xuexiang.xui.widget.edittext.materialedittext.MaterialEditText
                android:id="@+id/et_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="36dp"
                android:hint="输入申请备注"
              />

        </FrameLayout>

    </LinearLayout>

    <com.xuexiang.xui.widget.textview.supertextview.SuperButton
        android:id="@+id/btn_login"
        style="@style/SuperButton.Primary.Login"
        android:layout_marginTop="16dp"
        android:text="@string/title_login_register" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginBottom="40dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/register_tip"
                style="@style/TextStyle.Explain"
                android:layout_width="wrap_content"
                android:text="@string/agree_protocol"
                android:textColor="@color/xui_config_color_red" />

        </LinearLayout>

    </FrameLayout>


</LinearLayout>