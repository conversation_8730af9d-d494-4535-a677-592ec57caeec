<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2024 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/rootView"
    android:paddingStart="50dp"
    android:paddingEnd="50dp"
    tools:context="cn.geyuantz.reader.com.geyuantz.reader.fragment.style.StandardMainFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="40dp"
        android:orientation="horizontal">
        <cn.geyuantz.reader.component.GyBanner
            android:id="@+id/banner"
            android:layout_width="230dp"
            android:layout_height="100dp"
            android:gravity="start"
            app:bb_scale="0.4"
            />

        <ImageView
            android:id="@+id/banner1"
            android:layout_width="230dp"
            android:layout_height="100dp"
            android:scaleType="fitXY"
            android:layout_marginStart="10dp"
            app:bb_scale="0.4"
            />
    </LinearLayout>


    <!--模块-->
    <GridView
        android:id="@+id/grid_module"
        android:layout_width="match_parent"
        android:layout_height="130dp"
        android:layout_marginTop="@dimen/config_margin_20dp"
        android:stretchMode="columnWidth"
        android:numColumns="5"
        android:verticalSpacing="15dp"
        />


</LinearLayout>