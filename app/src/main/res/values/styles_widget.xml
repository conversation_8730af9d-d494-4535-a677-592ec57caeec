<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2019 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<resources>

    <!--放一些组件的样式-->
    <style name="WindowAnimStyle" parent="@android:style/Animation">
        <item name="android:activityOpenEnterAnimation">@anim/xpage_slide_in_right</item>
        <item name="android:activityOpenExitAnimation">@anim/xpage_slide_out_left</item>
        <item name="android:activityCloseEnterAnimation">@anim/xpage_slide_in_left</item>
        <item name="android:activityCloseExitAnimation">@anim/xpage_slide_out_right</item>
    </style>

    <style name="Container">
        <item name="android:paddingStart">?attr/xui_config_content_spacing_horizontal</item>
        <item name="android:paddingEnd">?attr/xui_config_content_spacing_horizontal</item>
    </style>

    <style name="InfoItem" parent="Container">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">50dp</item>
        <item name="sLeftViewMarginLeft">10dp</item>
        <item name="sRightViewMarginRight">0dp</item>
        <item name="sRightIconMarginRight">0dp</item>
        <item name="sLeftIconMarginLeft">0dp</item>
        <item name="sLeftTextSize">?attr/xui_config_size_content_text</item>
        <item name="sLeftTextColor">@color/xui_config_color_title_text</item>
        <item name="sCenterTextColor">@color/xui_config_color_black</item>
        <item name="sRightTextColor">@color/xui_config_color_explain_text</item>
        <item name="sRightTextSize">?attr/xui_config_size_content_text</item>
    </style>

    <style name="InfoItem.Account">
        <item name="sLeftViewWidth">110dp</item>
        <item name="sLeftViewGravity">left_center</item>
        <item name="sCenterViewGravity">left_center</item>
        <item name="sCenterViewMarginLeft">25dp</item>
        <item name="sRightTextSize">?attr/xui_config_size_content_text</item>
        <item name="sRightViewMarginRight">10dp</item>
        <item name="sRightIconRes">@drawable/icon_arrow_right_grey</item>
    </style>

    <style name="InfoItem.Account.NoEdit">
        <item name="sRightIconRes">@null</item>
    </style>

    <style name="RadiusImageView.Circle">
        <item name="android:scaleType">centerCrop</item>
        <item name="riv_is_circle">true</item>
        <item name="riv_border_width">0dp</item>
    </style>

    <style name="RadiusImage">
        <item name="android:scaleType">centerCrop</item>
        <item name="riv_border_width">0dp</item>
        <item name="riv_corner_radius">5dp</item>
    </style>

    <style name="PullDownStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="srlEnableOverScrollDrag">true</item>
        <item name="srlEnablePureScrollMode">true</item>
    </style>

    <style name="ScrollViewStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:overScrollMode">never</item>
    </style>

    <style name="SuperButton.Primary.Login">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:layout_marginStart">50dp</item>
        <item name="android:layout_marginEnd">50dp</item>
        <item name="sCornersRadius">20dp</item>
        <item name="sSelectorNormalColor">?attr/colorAccent</item>
        <item name="sSelectorPressedColor">?attr/colorAccent</item>
        <item name="android:textColor">@color/xui_config_color_white</item>
    </style>

    <style name="XUILayout">
        <item name="xui_borderWidth">0dp</item>
        <item name="xui_radius">8dp</item>
        <item name="xui_shadowElevation">6dp</item>
        <item name="android:background">@color/xui_config_color_white</item>
    </style>

</resources>