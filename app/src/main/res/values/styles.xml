<resources>

    <!-- 拓展主题 theme. -->
    <style name="XUITheme" parent="XUIBaseTheme">
        <!--设置默认窗口的动画样式-->
        <item name="android:windowAnimationStyle">@style/WindowAnimStyle</item>
    </style>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="XUITheme.Phone">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="xui_actionbar_height">?actionBarSize</item>
    </style>

    <style name="AppTheme.Launch">
        <item name="xui_config_color_splash_bg">@color/xui_config_color_white</item>
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <!--窗体背景，这个背景能在第一时间显示, 避免启动时白屏，黑屏问题-->
    <style name="AppTheme.Launch.App">
        <!--使用start_bg.png作为启动背景-->
        <item name="xui_config_splash_app_logo">@drawable/start_bg</item>
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />
    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!--DialogTheme，用于将Activity作为Dialog的主题-->
    <style name="DialogTheme" parent="XUITheme.Phone">
        <!--设置dialog的背景，此处为系统给定的透明值-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--Dialog的windowFrame框为无-->
        <item name="android:windowFrame">@null</item>
        <!--无标题-->
        <item name="android:windowNoTitle">true</item>　　　　　
        <!--是否浮现在activity之上-->
        <item name="android:windowIsFloating">true</item>
        <!--是否半透明-->
        <item name="android:windowIsTranslucent">true</item>
        <!--是否有覆盖-->
        <item name="android:windowContentOverlay">@null</item>
        <!--设置Activity出现方式-->
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <!--背景是否模糊显示-->
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <!-- FloatingActionButton 样式 -->
    <style name="ShapeAppearanceOverlay.App.FloatingActionButton" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

</resources>
