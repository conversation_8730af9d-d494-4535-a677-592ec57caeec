<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2019 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<!--侧边栏-->
<menu xmlns:android="http://schemas.android.com/apk/res/android">

    <group
        android:id="@+id/main_tab"
        android:checkableBehavior="single">

        <item
            android:id="@+id/nav_news"
            android:icon="@drawable/ic_menu_news"
            android:title="@string/menu_news" />

        <item
            android:id="@+id/nav_trending"
            android:icon="@drawable/ic_menu_trending"
            android:title="@string/menu_trending" />

        <item
            android:id="@+id/nav_profile"
            android:icon="@drawable/ic_menu_person"
            android:title="@string/menu_profile" />


    </group>

    <group
        android:id="@+id/other"
        android:checkableBehavior="single">

        <item
            android:id="@+id/nav_search"
            android:checkable="false"
            android:icon="@drawable/ic_menu_search"
            android:title="@string/menu_search" />

        <item
            android:id="@+id/nav_notifications"
            android:checkable="false"
            android:icon="@drawable/ic_menu_notifications"
            android:title="@string/menu_notifications" />

        <item
            android:id="@+id/nav_starred"
            android:checkable="false"
            android:icon="@drawable/ic_menu_star"
            android:title="@string/menu_starred" />
    </group>

    <group
        android:id="@+id/setting"
        android:checkableBehavior="none">
        <item
            android:id="@+id/nav_settings"
            android:checkable="false"
            android:icon="@drawable/ic_menu_settings"
            android:title="@string/menu_settings" />
        <item
            android:id="@+id/nav_about"
            android:checkable="false"
            android:icon="@drawable/ic_menu_about"
            android:title="@string/menu_about" />
    </group>


</menu>
