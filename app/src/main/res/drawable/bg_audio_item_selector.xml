<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 选中状态 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="#1AFF9800" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 正在播放状态 -->
    <item android:state_activated="true">
        <shape android:shape="rectangle">
            <solid android:color="#1AFF9800" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
</selector>
