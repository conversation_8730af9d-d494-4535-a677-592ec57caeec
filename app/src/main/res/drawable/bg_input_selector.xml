<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 聚焦状态 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="#FFFFFF" />
            <corners android:radius="24dp" />
            <stroke
                android:width="2dp"
                android:color="#4285F4" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#F5F5F5" />
            <corners android:radius="24dp" />
            <stroke
                android:width="1dp"
                android:color="#E0E0E0" />
        </shape>
    </item>
    
</selector>
