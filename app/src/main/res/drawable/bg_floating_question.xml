<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <layer-list>
            <!-- 阴影层 -->
            <item android:top="2dp" android:left="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="#40000000" />
                    <corners android:radius="25dp" />
                </shape>
            </item>
            <!-- 主体层 -->
            <item android:bottom="2dp" android:right="2dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#667EEA"
                        android:endColor="#764BA2"
                        android:angle="135" />
                    <corners android:radius="25dp" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- 默认状态 -->
    <item>
        <layer-list>
            <!-- 阴影层 -->
            <item android:top="3dp" android:left="3dp">
                <shape android:shape="rectangle">
                    <solid android:color="#30000000" />
                    <corners android:radius="25dp" />
                </shape>
            </item>
            <!-- 主体层 -->
            <item android:bottom="3dp" android:right="3dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#667EEA"
                        android:endColor="#764BA2"
                        android:angle="135" />
                    <corners android:radius="25dp" />
                    <stroke
                        android:width="1dp"
                        android:color="#80FFFFFF" />
                </shape>
            </item>
        </layer-list>
    </item>

</selector>
