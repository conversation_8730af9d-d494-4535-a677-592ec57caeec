<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2024 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/level_2_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="2dp">

    <!-- 现代化卡片容器 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/newspaper_card_bg"
        android:layout_marginBottom="2dp">

        <!-- 卡片内容 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center_vertical"
            android:background="@drawable/newspaper_card_ripple">

            <!-- 报纸图标容器 -->
            <FrameLayout
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginEnd="12dp">

                <!-- 背景圆形 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/newspaper_icon_bg" />

                <!-- 报纸图标 -->
                <TextView
                    android:id="@+id/tv_icon"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:text="📰" />

            </FrameLayout>

            <!-- 文字内容 -->
            <com.xuexiang.xui.widget.textview.supertextview.SuperTextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:sCenterTextSize="15sp"
                app:sCenterTextColor="@color/newspaper_title_text" />

            <!-- 箭头图标 -->
            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_arrow_forward"
                android:layout_marginStart="8dp"
                app:tint="@color/newspaper_arrow_color"
                android:alpha="0.6" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>