<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tv_category"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="8dp"
    android:background="@drawable/bg_category_bubble_selector"
    android:paddingStart="16dp"
    android:paddingTop="8dp"
    android:paddingEnd="16dp"
    android:paddingBottom="8dp"
    android:textColor="@color/category_text_selector"
    android:textSize="12sp"
    android:gravity="center"
    android:minWidth="60dp"
    android:singleLine="true"
    android:ellipsize="end"
    android:clickable="true"
    android:focusable="true"
    tools:text="科幻小说" />
