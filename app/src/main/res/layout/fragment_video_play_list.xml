<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context="cn.geyuantz.reader.fragment.audio.AudioListFragment">

    <!-- 视频播放器区域 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="220dp"
        android:background="#000000">

        <com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
            android:id="@+id/detail_player"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </FrameLayout>

    <!-- 集数列表区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="#F8F9FA">

        <!-- 集数标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical"
            android:background="@android:color/white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="选集"
                android:textColor="#333333"
                android:textSize="18sp"
                android:textStyle="bold" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tv_episode_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="共15集"
                android:textColor="#666666"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E5E5E5" />

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srlEnableAutoLoadMore="true"
            app:srlEnableLoadMore="true">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#F8F9FA"
                android:overScrollMode="never"
                android:padding="4dp"
                android:clipToPadding="false" />

        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    </LinearLayout>


</LinearLayout>