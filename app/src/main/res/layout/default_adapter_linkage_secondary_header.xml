<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="12dp"
    android:paddingVertical="6dp">

    <!-- 现代化标题容器 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/white"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            android:paddingVertical="12dp">

            <!-- 装饰性指示器 -->
            <View
                android:layout_width="3dp"
                android:layout_height="18dp"
                android:background="@drawable/newspaper_category_indicator"
                android:layout_marginEnd="12dp" />

            <!-- 标题文字 -->
            <TextView
                android:id="@+id/secondary_header"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="15sp"
                android:textStyle="bold"
                android:textColor="@color/newspaper_title_color"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:ellipsize="end" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>
