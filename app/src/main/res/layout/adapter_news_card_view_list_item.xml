<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2020 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<com.xuexiang.xui.widget.layout.XUIFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/card_view"
    style="@style/XUILayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/config_margin_16dp"
    android:layout_marginTop="@dimen/config_margin_4dp"
    android:layout_marginEnd="@dimen/config_margin_16dp"
    android:layout_marginBottom="@dimen/config_margin_4dp"
    android:paddingStart="@dimen/config_padding_16dp"
    android:paddingTop="@dimen/config_padding_4dp"
    android:paddingEnd="@dimen/config_padding_16dp"
    android:paddingBottom="@dimen/config_padding_8dp">

    <include layout="@layout/layout_news_card_item" />

</com.xuexiang.xui.widget.layout.XUIFrameLayout>