<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    tools:context="cn.geyuantz.reader.fragment.book.BookInfoFragment">

    <!-- 全屏背景图 -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/bg_booklist"
        android:scaleType="centerCrop"
        android:fitsSystemWindows="false" />

    <!-- 返回按钮 -->
    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="40dp"
        android:padding="12dp"
        android:src="@drawable/ic_arrow_left"
        android:background="@drawable/bg_back_button"
        android:clickable="true"
        android:focusable="true"
        android:elevation="10dp"
        android:translationZ="10dp" />

    <!-- 内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="100dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingBottom="20dp"
        android:clipToPadding="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <!-- 图书封面容器 -->
            <FrameLayout
                android:id="@+id/fl_cover_container"
                android:layout_width="180dp"
                android:layout_height="240dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="20dp">

                <!-- 图书封面 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_book_cover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="8dp">

                    <ImageView
                        android:id="@+id/iv_book_cover"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="fitXY"
                        android:background="@android:color/darker_gray"
                        android:src="@mipmap/ic_launcher" />

                </androidx.cardview.widget.CardView>

                <!-- 二维码卡片（默认隐藏） -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_qr_code"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="8dp"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:background="@android:color/white"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="推荐使用浏览器扫码阅读"
                            android:textSize="12sp"
                            android:textColor="@android:color/black"
                            android:layout_marginBottom="8dp" />

                        <ImageView
                            android:id="@+id/iv_qr_code"
                            android:layout_width="140dp"
                            android:layout_height="140dp"
                            android:background="@android:color/white"
                            android:padding="4dp"
                            android:src="@mipmap/ic_launcher" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </FrameLayout>

            <!-- 图书信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/bg_book_info_card"
                android:padding="16dp"
                android:layout_marginBottom="20dp">

                <!-- 书名 -->
                <TextView
                    android:id="@+id/tv_book_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="书籍名称"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:gravity="center"
                    android:layout_marginBottom="10dp"
                    android:maxLines="2"
                    android:ellipsize="end" />

                <!-- 作者 -->
                <TextView
                    android:id="@+id/tv_book_author"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="作者：未知"
                    android:textSize="14sp"
                    android:textColor="#E0FFFFFF"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <!-- 简介 -->
                <TextView
                    android:id="@+id/tv_book_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="点击下方按钮开始阅读这本精彩的图书"
                    android:textSize="12sp"
                    android:textColor="#C0FFFFFF"
                    android:gravity="center"
                    android:maxLines="2"
                    android:ellipsize="end"
                    android:lineSpacingExtra="2dp" />

            </LinearLayout>

            <!-- 阅读按钮区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginBottom="20dp">

                <!-- 本机阅读按钮 -->
                <Button
                    android:id="@+id/btn_read_local"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="本机阅读"
                    android:textSize="14sp"
                    android:textColor="@android:color/white"
                    android:background="@drawable/bg_button_primary"
                    android:drawableStart="@drawable/ic_book_open"
                    android:drawablePadding="6dp"
                    android:gravity="center" />

                <!-- 扫码阅读按钮 -->
                <Button
                    android:id="@+id/btn_read_qrcode"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="扫码阅读"
                    android:textSize="14sp"
                    android:textColor="@android:color/white"
                    android:background="@drawable/bg_button_secondary"
                    android:drawableStart="@drawable/ic_qr_code"
                    android:drawablePadding="6dp"
                    android:gravity="center" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</FrameLayout>