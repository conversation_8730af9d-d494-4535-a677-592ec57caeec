<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false">

    <!-- 全屏背景图 -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/bg_booklist"
        android:scaleType="centerCrop"
        android:fitsSystemWindows="false" />

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="110dp"
        android:clipToPadding="false"
        android:fitsSystemWindows="false"
        app:srlEnableAutoLoadMore="true"
        app:srlEnableLoadMore="true">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/transparent"
            android:overScrollMode="never"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:clipToPadding="false" />

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    <!-- 加载提示 -->
    <LinearLayout
        android:id="@+id/ll_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="正在加载图书..."
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            android:background="@drawable/bg_loading_text"
            android:padding="12dp" />

    </LinearLayout>

    <!-- 顶部工具栏 - 放在最上层确保可以点击 -->
    <include
        android:id="@+id/toolbar"
        layout="@layout/layout_book_list_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</FrameLayout>