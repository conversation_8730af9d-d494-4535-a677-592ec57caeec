<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2021 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:showIn="@layout/adapter_news_card_view_list_item">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="5dp"
        android:paddingBottom="5dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.xuexiang.xui.widget.imageview.RadiusImageView
                android:id="@+id/iv_avatar"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@mipmap/ic_launcher"
                app:riv_is_circle="true" />

            <TextView
                android:id="@+id/tv_user_name"
                style="@style/TextStyle.Explain"
                android:layout_gravity="end|center_vertical"
                android:layout_marginStart="4dp"
                android:text="xuexiangjys"
                android:textColor="@color/xui_config_color_pure_black" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_tag"
            style="@style/TextStyle.Explain"
            android:layout_gravity="end|center_vertical"
            android:text="Java" />

    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                style="@style/TextStyle.Content"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:maxLines="2"
                android:singleLine="false"
                android:text="深度解析RocketMQ消息发送的高可用设计"
                android:textColor="@color/xui_config_color_pure_black" />

            <TextView
                android:id="@+id/tv_summary"
                style="@style/TextStyle.Explain"
                android:layout_marginTop="3dp"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:maxLines="2"
                android:singleLine="false"
                android:text="从rocketmq topic的创建机制可知，一个topic对应有多个消息队列，那么我们在发送消息时，是如何选择消息队列进行发送的？"
                android:textColor="?attr/xui_config_color_content_text" />

        </LinearLayout>

        <com.xuexiang.xui.widget.imageview.RadiusImageView
            android:id="@+id/iv_image"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginStart="8dp"
            android:scaleType="centerCrop"
            android:src="@drawable/xui_ic_default_img"
            app:riv_border_width="0dp"
            app:riv_corner_radius="5dp" />

    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_praise"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_praise" />

            <TextView
                android:id="@+id/tv_praise"
                style="@style/TextStyle.Explain"
                android:layout_marginStart="5dp"
                android:text="点赞" />


            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_comment"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginStart="30dp"
                android:src="@drawable/ic_comment" />

            <TextView
                android:id="@+id/tv_comment"
                style="@style/TextStyle.Explain"
                android:layout_marginStart="5dp"
                android:text="评论" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_read"
            style="@style/TextStyle.Explain"
            android:layout_gravity="center_vertical|end"
            android:text="阅读量 123" />

    </FrameLayout>


</LinearLayout>