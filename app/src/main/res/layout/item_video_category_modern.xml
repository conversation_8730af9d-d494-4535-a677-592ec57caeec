<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:cardUseCompatPadding="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- 分类图标 -->
        <FrameLayout
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="12dp">

            <!-- 背景圆圈 -->
            <View
                android:id="@+id/icon_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_category_icon_circle" />

            <!-- 图标 -->
            <ImageView
                android:id="@+id/iv_category_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_video_category"
                android:tint="@android:color/white"
                tools:ignore="UseAppTint" />

        </FrameLayout>

        <!-- 分类名称 -->
        <TextView
            android:id="@+id/tv_category_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/text_color_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center_vertical"
            tools:text="电影" />

        <!-- 箭头图标 -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_right"
            android:tint="@color/text_color_hint"
            tools:ignore="UseAppTint" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
