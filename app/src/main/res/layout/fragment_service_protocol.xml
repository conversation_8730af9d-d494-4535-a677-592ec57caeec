<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2021 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/xui_config_color_white"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        style="@style/ScrollViewStyle"
        android:layout_marginTop="@dimen/config_margin_10dp">

        <LinearLayout
            style="@style/Container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_protocol_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/config_margin_8dp"
                android:singleLine="false"
                android:textSize="13sp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>