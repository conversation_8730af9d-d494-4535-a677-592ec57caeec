<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2019 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/PullDownStyle"
    android:background="?attr/xui_config_color_background">

    <androidx.core.widget.NestedScrollView style="@style/ScrollViewStyle">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingTop="20dp">

            <com.xuexiang.xui.widget.textview.supertextview.SuperTextView
                style="@style/InfoItem.Account"
                android:layout_height="60dp"
                app:sLeftTextString="头像">

                <com.xuexiang.xui.widget.imageview.RadiusImageView
                    android:id="@+id/riv_head_pic"
                    style="@style/RadiusImageView.Circle"
                    android:layout_width="38dp"
                    android:layout_height="38dp"
                    android:layout_centerVertical="true"
                    android:layout_toStartOf="@id/sRightImgId"
                    app:srcCompat="@drawable/ic_default_head" />

            </com.xuexiang.xui.widget.textview.supertextview.SuperTextView>

            <com.xuexiang.xui.widget.textview.supertextview.SuperTextView
                style="@style/InfoItem.Account"
                app:sLeftTextString="账号" />

            <com.xuexiang.xui.widget.textview.supertextview.SuperTextView
                style="@style/InfoItem.Account"
                app:sLeftTextString="通知" />

            <com.xuexiang.xui.widget.textview.supertextview.SuperTextView
                style="@style/InfoItem.Account"
                app:sDividerLineType="none"
                app:sLeftTextString="收藏" />


            <com.xuexiang.xui.widget.textview.supertextview.SuperTextView
                style="@style/InfoItem.Account"
                android:layout_marginTop="20dp"
                app:sDividerLineType="none"
                app:sLeftTextString="意见反馈" />

            <com.xuexiang.xui.widget.textview.supertextview.SuperTextView
                android:id="@+id/menu_settings"
                style="@style/InfoItem.Account"
                android:layout_marginTop="20dp"
                app:sLeftTextString="设置" />

            <com.xuexiang.xui.widget.textview.supertextview.SuperTextView
                android:id="@+id/menu_about"
                style="@style/InfoItem.Account"
                app:sDividerLineType="none"
                app:sLeftTextString="关于" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</com.scwang.smart.refresh.layout.SmartRefreshLayout>
