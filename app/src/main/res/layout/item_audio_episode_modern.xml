<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardUseCompatPadding="true">

    <LinearLayout
        android:id="@+id/ll_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical"
        android:background="@drawable/bg_audio_item_selector">

        <!-- 音频图标 -->
        <FrameLayout
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginEnd="12dp">

            <!-- 背景圆圈 -->
            <View
                android:id="@+id/episode_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_audio_number_modern" />

            <!-- 集数文字 -->
            <TextView
                android:id="@+id/tv_episode_number"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:textColor="@android:color/white"
                android:textSize="13sp"
                android:textStyle="bold"
                tools:text="01" />

        </FrameLayout>

        <!-- 音频信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 音频标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/audio_title_color_selector"
                android:textSize="15sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="第一集：精彩开始" />

        </LinearLayout>

        <!-- 播放状态指示器 -->
        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif_play_status"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/music_choose"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
