<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="6dp"
    android:layout_marginEnd="6dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    android:background="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="0dp">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:scaleType="fitXY"
            android:adjustViewBounds="false"
            android:background="@android:color/darker_gray" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="4dp"
            android:textSize="10sp"
            android:textColor="@android:color/black"
            android:textAlignment="center"
            android:maxLines="2"
            android:ellipsize="end"
            android:lineSpacingExtra="1dp"
            android:gravity="center_vertical|center_horizontal"
            android:background="@android:color/white"
            tools:text="书籍标题" />

    </LinearLayout>

</androidx.cardview.widget.CardView>