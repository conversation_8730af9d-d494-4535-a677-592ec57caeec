<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="72dp"
        android:orientation="vertical">

        <com.xuexiang.xui.widget.alpha.XUIAlphaImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:padding="@dimen/config_margin_10dp"
            app:srcCompat="@drawable/ic_action_close_white" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/config_margin_10dp"
            android:layout_marginTop="@dimen/config_margin_16dp"
            android:layout_marginEnd="@dimen/config_margin_10dp"
            android:background="@drawable/bg_dialog_common_tip_corner_white"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="@dimen/config_margin_10dp"
                android:paddingEnd="@dimen/config_margin_10dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_top"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/config_margin_8dp"
                    android:layout_marginEnd="@dimen/config_margin_8dp"
                    app:srcCompat="@drawable/img_guide_tip_top" />

                <TextView
                    android:id="@+id/tv_title"
                    style="@style/TextStyle.Content"
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/config_margin_16dp"
                    android:text="@string/tip_title"
                    android:textColor="?attr/colorAccent" />

                <TextView
                    android:id="@+id/tv_content"
                    style="@style/TextStyle.Explain"
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/config_margin_10dp"
                    android:background="@color/config_color_select_bg"
                    android:gravity="start|center_vertical"
                    android:lineSpacingExtra="@dimen/config_margin_8dp"
                    android:paddingStart="@dimen/config_margin_20dp"
                    android:paddingTop="@dimen/config_margin_10dp"
                    android:paddingEnd="@dimen/config_margin_20dp"
                    android:paddingBottom="@dimen/config_margin_10dp"
                    android:singleLine="false"
                    android:textColor="@color/xui_config_color_primary_text"
                    tools:text="获取更多资讯，欢迎关注我的微信公众号：【我的Android开源之旅】" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/config_margin_8dp"
                    android:layout_marginBottom="@dimen/config_margin_8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatCheckBox
                        android:id="@+id/cb_ignore"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:button="@drawable/xui_icon_checkbox"
                        android:scaleX="0.8"
                        android:scaleY="0.8" />

                    <TextView
                        style="@style/TextStyle.Explain"
                        android:layout_marginStart="@dimen/config_margin_4dp"
                        android:text="@string/tip_ignore_message" />
                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/config_margin_5dp"
                android:background="?attr/xui_config_color_separator_light" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:orientation="horizontal">

                <com.xuexiang.xui.widget.alpha.XUIAlphaTextView
                    android:id="@+id/tv_previous"
                    style="@style/TextStyle.Explain"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="@string/label_previous"
                    android:textColor="@color/xui_config_color_primary_text" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="?attr/xui_config_color_separator_light" />

                <com.xuexiang.xui.widget.alpha.XUIAlphaTextView
                    android:id="@+id/tv_next"
                    style="@style/TextStyle.Explain"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="@string/label_next"
                    android:textColor="@color/xui_config_color_primary_text" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>


</LinearLayout>