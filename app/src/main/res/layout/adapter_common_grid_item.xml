<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2020 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->
<com.xuexiang.xui.widget.alpha.XUIAlphaLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:background="@drawable/bg_module_card"
        android:elevation="4dp"
        android:padding="8dp">

        <com.xuexiang.xui.widget.imageview.RadiusImageView
            android:id="@+id/riv_item"
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:layout_gravity="center"
            tools:src="@mipmap/ic_launcher" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/TextStyle.Title"
            android:layout_gravity="center"
            android:textColor="@color/xui_config_color_white"
            android:textSize="16sp"
            tools:text="菜" />

    </FrameLayout>

    <TextView
        android:id="@+id/tv_sub_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        android:ellipsize="end"
        android:textSize="11sp"
        android:textColor="#8B0000"
        android:textStyle="bold"
        tools:text="党史学习教育" />

</com.xuexiang.xui.widget.alpha.XUIAlphaLinearLayout>