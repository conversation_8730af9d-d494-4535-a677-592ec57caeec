<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2019 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:minHeight="?attr/listPreferredItemHeight"
    android:orientation="vertical"
    android:paddingStart="?attr/xui_config_content_spacing_horizontal"
    android:paddingEnd="?attr/xui_config_content_spacing_horizontal">

    <TextView
        android:id="@+id/tv_title"
        style="@style/TextStyle.Title"
        tools:text="主标题" />

    <TextView
        android:id="@+id/tv_sub_title"
        style="@style/TextStyle.Explain"
        android:layout_marginTop="4dp"
        tools:text="副标题" />

</LinearLayout>