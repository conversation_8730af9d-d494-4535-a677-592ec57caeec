<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2019 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="?attr/colorAccent"
    app:contentInsetLeft="0dp"
    app:contentInsetStart="0dp"
    app:titleTextColor="@android:color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/colorAccent">

        <com.xuexiang.xui.widget.alpha.XUIAlphaImageView
            android:id="@+id/iv_back"
            android:layout_width="55dp"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:scaleType="center"
            android:src="@drawable/ic_web_back" />

        <View
            android:id="@+id/view_line"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:layout_toEndOf="@id/iv_back"
            android:background="@color/xui_config_color_white" />

        <com.xuexiang.xui.widget.alpha.XUIAlphaImageView
            android:id="@+id/iv_finish"
            android:layout_width="55dp"
            android:layout_height="match_parent"
            android:layout_toEndOf="@id/view_line"
            android:scaleType="center"
            android:src="@drawable/ic_web_close" />

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:singleLine="true"
            android:textColor="@android:color/white"
            android:textSize="14sp" />

        <!--去除 -->
        <com.xuexiang.xui.widget.alpha.XUIAlphaImageView
            android:id="@+id/iv_more"
            android:layout_width="55dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:scaleType="center"
            android:visibility="gone"
            android:src="@drawable/ic_web_more" />


    </RelativeLayout>


</androidx.appcompat.widget.Toolbar>

