<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/bg_newspaper_gradient">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="12dp"
        android:paddingBottom="8dp"
        android:paddingHorizontal="16dp"
        android:gravity="center_vertical">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            app:tint="@color/newspaper_title_color" />

        <!-- 标题区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="12dp"
            android:gravity="end">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📰 数字报纸"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/newspaper_title_color"
                android:layout_marginBottom="2dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="精选优质报纸，随时随地阅读"
                android:textSize="12sp"
                android:textColor="@color/newspaper_subtitle_color"
                android:alpha="0.8" />

        </LinearLayout>

    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@drawable/divider_gradient"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="4dp" />

    <!-- 报纸列表 -->
    <com.kunminx.linkage.LinkageRecyclerView
        android:id="@+id/linkage"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="8dp"
        android:clipToPadding="false" />

</LinearLayout>