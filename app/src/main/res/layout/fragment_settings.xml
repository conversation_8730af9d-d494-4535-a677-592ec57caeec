<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2019 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/PullDownStyle">

    <androidx.core.widget.NestedScrollView style="@style/ScrollViewStyle">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingTop="20dp">
            <com.xuexiang.xui.widget.textview.supertextview.SuperTextView
                android:id="@+id/request_help"
                style="@style/InfoItem.Account"
                app:sLeftTextString="请求协助" />

            <com.xuexiang.xui.widget.textview.supertextview.SuperTextView
                android:id="@+id/menu_device_info"
                style="@style/InfoItem.Account"
                app:sLeftTextString="设备信息" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</com.scwang.smart.refresh.layout.SmartRefreshLayout>