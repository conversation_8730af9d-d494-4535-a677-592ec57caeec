<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardUseCompatPadding="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp">

        <!-- 视频封面 -->
        <FrameLayout
            android:layout_width="110dp"
            android:layout_height="82dp"
            android:layout_marginEnd="12dp">

            <!-- 封面图片 -->
            <ImageView
                android:id="@+id/iv_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY"
                android:background="@drawable/bg_video_cover_modern"
                tools:src="@drawable/ic_logo_app" />

            <!-- 播放按钮覆盖层 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_video_overlay_modern" />

            <!-- 播放图标 -->
            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_play_modern"
                android:alpha="0.9"
                tools:ignore="UseAppTint" />

        </FrameLayout>

        <!-- 视频信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center_vertical"
            android:paddingStart="4dp">

            <!-- 视频标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#2C2C2C"
                android:textSize="16sp"
                android:textStyle="bold"
                android:maxLines="2"
                android:ellipsize="end"
                android:lineSpacingExtra="2dp"
                android:layout_marginBottom="8dp"
                tools:text="精彩视频标题内容展示" />

            <!-- 视频数量 -->
            <TextView
                android:id="@+id/tv_video_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="共15个视频"
                android:textColor="#8A8A8A"
                android:textSize="13sp"
                android:background="@drawable/bg_video_count_label"
                android:paddingHorizontal="8dp"
                android:paddingVertical="3dp"
                android:drawableStart="@drawable/ic_video_count_small"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                tools:text="共15个视频" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
