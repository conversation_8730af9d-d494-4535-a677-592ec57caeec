<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@android:color/transparent"
    android:paddingTop="18dp"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingBottom="8dp">

    <!-- 第一行：返回按钮和搜索框 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_arrow_left"
            android:background="@drawable/bg_back_button"
            android:padding="8dp"
            android:layout_marginEnd="12dp"
            android:scaleType="centerInside"
            android:tint="@android:color/white"
            android:clickable="true"
            android:focusable="true" />

        <!-- 搜索框容器 -->
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/bg_search_box">

            <!-- 搜索输入框 -->
            <EditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_toStartOf="@+id/iv_search"
                android:background="@android:color/transparent"
                android:hint="搜索图书..."
                android:textColorHint="#80FFFFFF"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:paddingStart="16dp"
                android:paddingEnd="8dp"
                android:singleLine="true"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:clickable="true" />

            <!-- 搜索按钮 -->
            <ImageView
                android:id="@+id/iv_search"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_search"
                android:padding="10dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:tint="#80FFFFFF"
                android:scaleType="centerInside"
                android:clickable="true"
                android:focusable="true" />

        </RelativeLayout>

    </LinearLayout>

    <!-- 第二行：分类气泡横向滚动 -->
    <HorizontalScrollView
        android:id="@+id/hsv_categories"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        android:overScrollMode="never"
        android:fadingEdge="horizontal"
        android:fadingEdgeLength="16dp">

        <LinearLayout
            android:id="@+id/ll_categories"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingStart="4dp"
            android:paddingEnd="4dp" />

    </HorizontalScrollView>

</LinearLayout>
