<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/layout_group"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingVertical="8dp"
    android:paddingHorizontal="12dp">

    <TextView
        android:id="@+id/tv_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/newspaper_title_color"
        android:paddingVertical="12dp"
        android:paddingHorizontal="16dp"
        android:background="@drawable/newspaper_category_bg"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:ellipsize="end" />

</LinearLayout>
