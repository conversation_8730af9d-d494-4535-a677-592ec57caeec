<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2024 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/rootView"
    tools:context="cn.geyuantz.reader.com.geyuantz.reader.fragment.style.StandardMainFragment">

    <!--banner-->
    <cn.geyuantz.reader.component.GyBanner
        android:id="@+id/banner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginLeft="@dimen/config_margin_10dp"
        android:layout_marginRight="@dimen/config_margin_10dp"
        app:bb_scale="0.4"
        />

    <!--模块 - 两排水平滑动-->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_modules"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            />

        <!-- 左侧箭头提示 - 向左翻页 -->
        <FrameLayout
            android:id="@+id/fl_arrow_left"
            android:layout_width="30dp"
            android:layout_height="60dp"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="3dp"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_arrow_left"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_arrow_left"
                android:alpha="0.8" />

        </FrameLayout>

        <!-- 右侧箭头提示 - 向右翻页 -->
        <FrameLayout
            android:id="@+id/fl_arrow_right"
            android:layout_width="30dp"
            android:layout_height="60dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="3dp"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_arrow_right"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_arrow_right"
                android:alpha="0.8" />

        </FrameLayout>

    </RelativeLayout>


</LinearLayout>