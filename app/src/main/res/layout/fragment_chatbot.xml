<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context="cn.geyuantz.reader.fragment.ai.ChatbotFragment">

    <!-- 消息列表容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- 消息列表 -->
        <cn.jiguang.imui.messages.MessageList
            android:id="@+id/msg_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#F8F9FA"
            app:avatarHeight="42dp"
            app:avatarWidth="42dp"
            app:showReceiverDisplayName="false"
            app:showSenderDisplayName="false"
            app:bubbleMaxWidth="0.70"
            app:dateTextSize="12sp"
            app:receiveBubbleColor="#E8E8E8"
            app:receiveBubblePaddingLeft="18dp"
            app:receiveBubblePaddingRight="18dp"
            app:receiveTextColor="#333333"
            app:receiveTextSize="15sp"
            app:sendBubbleColor="#4285F4"
            app:sendBubblePaddingLeft="18dp"
            app:sendBubblePaddingRight="18dp"
            app:sendTextColor="#FFFFFF"
            app:sendTextSize="15sp" />

        <!-- 悬浮问题区域 -->
        <LinearLayout
            android:id="@+id/ll_floating_questions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="40dp"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- 问题1 -->
            <TextView
                android:id="@+id/tv_question_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginBottom="12dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/bg_floating_question_modern"
                android:clickable="true"
                android:focusable="true"
                android:fontFamily="sans-serif-medium"
                android:letterSpacing="0.01"
                android:lineSpacingExtra="1dp"
                android:maxWidth="260dp"
                android:paddingStart="14dp"
                android:paddingEnd="14dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="💡 你能帮我解答学习问题吗？"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

            <!-- 问题2 -->
            <TextView
                android:id="@+id/tv_question_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginBottom="12dp"
                android:layout_marginEnd="24dp"
                android:background="@drawable/bg_floating_question_modern"
                android:clickable="true"
                android:focusable="true"
                android:fontFamily="sans-serif-medium"
                android:letterSpacing="0.01"
                android:lineSpacingExtra="1dp"
                android:maxWidth="260dp"
                android:paddingStart="14dp"
                android:paddingEnd="14dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="🚀 你有什么特殊的功能吗？"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

            <!-- 问题3 -->
            <TextView
                android:id="@+id/tv_question_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginEnd="16dp"
                android:background="@drawable/bg_floating_question_modern"
                android:clickable="true"
                android:focusable="true"
                android:fontFamily="sans-serif-medium"
                android:letterSpacing="0.01"
                android:lineSpacingExtra="1dp"
                android:maxWidth="260dp"
                android:paddingStart="14dp"
                android:paddingEnd="14dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="💬 如何更好地与你交流？"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 右下角问号按钮 - 优化设计 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_help"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="24dp"
            android:src="@drawable/ic_help_elegant"
            app:fabSize="mini"
            app:backgroundTint="#667eea"
            app:tint="#FFFFFF"
            app:elevation="8dp"
            app:hoveredFocusedTranslationZ="12dp"
            app:pressedTranslationZ="16dp"
            app:borderWidth="0dp"
            app:rippleColor="#80FFFFFF" />

    </FrameLayout>
    <!-- 现代化的聊天输入界面 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@drawable/bg_chat_input_container"
        android:elevation="12dp">

        <!-- 输入框容器 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="12dp"
            android:background="@drawable/bg_input_selector"
            android:orientation="horizontal"
            android:paddingStart="16dp"
            android:paddingEnd="12dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp">

            <!-- 输入框 -->
            <EditText
                android:id="@+id/et_message_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:hint="来和我聊天吧..."
                android:textColorHint="#999999"
                android:textColor="#333333"
                android:textSize="16sp"
                android:fontFamily="sans-serif"
                android:maxLines="4"
                android:scrollbars="vertical"
                android:inputType="textMultiLine|textCapSentences"
                android:imeOptions="actionSend" />

        </LinearLayout>

        <!-- 发送按钮 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_send"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:src="@drawable/ic_send_modern"
            app:fabSize="mini"
            app:backgroundTint="#4285F4"
            app:tint="#FFFFFF"
            app:elevation="6dp"
            app:borderWidth="0dp" />

    </LinearLayout>

</LinearLayout>