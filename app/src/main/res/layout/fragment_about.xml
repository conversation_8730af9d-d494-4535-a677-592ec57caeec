<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2019 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~        http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/PullDownStyle">

    <androidx.core.widget.NestedScrollView style="@style/ScrollViewStyle">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingTop="40dp"
            android:paddingBottom="25dp">

            <!--   请修改为自己的应用图标   -->
            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="165dp"
                android:layout_height="165dp"
                android:contentDescription="Logo"
                app:srcCompat="@drawable/ic_logo_app" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:text="@string/app_name"
                android:textColor="@color/xui_config_color_gray_3"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:textColor="@color/xui_config_color_gray_3"
                android:textSize="16sp" />

            <com.xuexiang.xui.widget.grouplist.XUIGroupListView
                android:id="@+id/about_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="35dp" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tv_copyright"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:gravity="center_horizontal"
                android:textColor="@color/xui_config_color_gray_7" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</com.scwang.smart.refresh.layout.SmartRefreshLayout>
