<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2024 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    tools:context="cn.geyuantz.reader.com.geyuantz.reader.fragment.style.StandardMainFragment">

    <!--banner-->
    <cn.geyuantz.reader.component.GyBanner
        android:id="@+id/banner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginLeft="@dimen/config_margin_10dp"
        android:layout_marginRight="@dimen/config_margin_10dp"
        app:bb_scale="0.4"
        />

    <!--模块-->
    <GridView
        android:id="@+id/grid_module"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/config_margin_20dp"
        android:layout_marginLeft="@dimen/config_margin_10dp"
        android:layout_marginRight="@dimen/config_margin_10dp"
        android:stretchMode="columnWidth"
        android:numColumns="4"
        android:verticalSpacing="15dp"
        />


</LinearLayout>