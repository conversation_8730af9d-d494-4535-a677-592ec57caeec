<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2019 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/nav_header"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/colorAccent"
    android:orientation="vertical">

    <Space
        android:layout_width="wrap_content"
        android:layout_height="20dp" />

    <LinearLayout
        android:layout_width="240dp"
        android:layout_height="wrap_content"
        android:baselineAligned="false"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="24dp"
        android:paddingRight="24dp">

        <com.xuexiang.xui.widget.imageview.RadiusImageView
            android:id="@+id/iv_avatar"
            android:layout_width="64dp"
            android:layout_height="64dp"
            app:riv_border_color="@color/xui_config_color_gray_6"
            app:riv_border_width="1px"
            app:riv_is_circle="true"
            app:riv_selected_border_color="@color/xui_config_color_gray_4"
            app:riv_selected_border_width="1px"
            app:riv_selected_mask_color="@color/xui_config_color_gray_8" />

        <TextView
            android:id="@+id/tv_avatar"
            style="@style/TextStyle.Title"
            android:layout_marginStart="?attr/xui_config_content_spacing_horizontal" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_sign"
        style="@style/TextStyle.Explain"
        android:layout_width="240dp"
        android:layout_marginTop="?attr/xui_config_content_spacing_vertical"
        android:gravity="start"
        android:paddingLeft="24dp"
        android:paddingRight="24dp"
        android:singleLine="false" />

    <Space
        android:layout_width="wrap_content"
        android:layout_height="20dp" />

</LinearLayout>