<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_audio_player_modern"
    android:padding="24dp">

    <!-- 音频信息区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <!-- 音频封面 -->
        <androidx.cardview.widget.CardView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <pl.droidsonroids.gif.GifImageView
                android:id="@+id/gif_audio_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/music" />

        </androidx.cardview.widget.CardView>

        <!-- 音频标题 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_audio_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:maxLines="2"
                android:ellipsize="end"
                android:gravity="center_vertical"
                tools:text="蜀道难" />

        </LinearLayout>

    </LinearLayout>

    <!-- 播放进度区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="20dp">

        <!-- 进度条 -->
        <SeekBar
            android:id="@+id/seekbar_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:progressTint="#FFFFFF"
            android:thumbTint="#FFFFFF"
            android:secondaryProgressTint="#66FFFFFF"
            android:progressBackgroundTint="#33FFFFFF"
            android:max="100"
            android:progress="5" />

        <!-- 时间显示 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_current_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#CCFFFFFF"
                android:textSize="12sp"
                tools:text="00:01" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tv_total_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#CCFFFFFF"
                android:textSize="12sp"
                tools:text="03:13" />

        </LinearLayout>

    </LinearLayout>

    <!-- 播放控制区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <!-- 上一首按钮 -->
        <ImageButton
            android:id="@+id/btn_previous"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="24dp"
            android:background="@drawable/bg_audio_control_button"
            android:src="@drawable/ic_skip_previous"
            android:tint="@android:color/white"
            android:contentDescription="上一首"
            tools:ignore="UseAppTint" />

        <!-- 播放/暂停按钮 -->
        <androidx.cardview.widget.CardView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginHorizontal="24dp"
            app:cardCornerRadius="32dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@android:color/white">

            <ImageButton
                android:id="@+id/btn_play_pause"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_play_arrow_large"
                android:tint="#FF9800"
                android:contentDescription="播放/暂停"
                tools:ignore="UseAppTint" />

        </androidx.cardview.widget.CardView>

        <!-- 下一首按钮 -->
        <ImageButton
            android:id="@+id/btn_next"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="24dp"
            android:background="@drawable/bg_audio_control_button"
            android:src="@drawable/ic_skip_next"
            android:tint="@android:color/white"
            android:contentDescription="下一首"
            tools:ignore="UseAppTint" />

    </LinearLayout>

</LinearLayout>
