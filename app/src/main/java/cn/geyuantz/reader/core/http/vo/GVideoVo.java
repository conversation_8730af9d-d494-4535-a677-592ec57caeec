package cn.geyuantz.reader.core.http.vo;

import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * 视频视图对象 g_video
 *
 * <AUTHOR>
 * @date 2024-04-04
 */
@Data
public class GVideoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 视频编号
     */
    private Long videoId;

    /**
     * 父视频编号
     */
    private Long parentId;

    /**
     * 视频名称
     */
    private String videoName;

    /**
     * 作者
     */
    private String videoAuthor;

    /**
     * 视频内容
     */
    private String videoInfo;

    /**
     * 视频数量
     */
    private Long videoCount;

    /**
     * 浏览数
     */
    private Long videoVisitCount;

    /**
     * 封面图
     */
    private String videoCover;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 状态
     */
    private Long status;

    /**
     * 排序
     */
    private Long sort;


}
