/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.core.http.vo;

import java.util.List;
import lombok.Data;

/**
 * 机器人详情信息视图对象
 *
 * <AUTHOR>
 * @since 2024/12/29
 */
@Data
public class RobotDetailVo {
    /**
     * 机器人ID
     */
    private Long robotId;
    
    /**
     * 机器人名称
     */
    private String robotName;
    
    /**
     * 机器人头像
     */
    private String robotAvatar;
    
    /**
     * 机器人描述
     */
    private String robotDescription;
    
    /**
     * 开场白
     */
    private String robotGreeting;
    
    /**
     * 开场问题列表
     */
    private List<String> robotQuestions;
}
