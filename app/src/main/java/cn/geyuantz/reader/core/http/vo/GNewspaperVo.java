package cn.geyuantz.reader.core.http.vo;

import com.kunminx.linkage.bean.BaseGroupedItem;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

import java.io.Serializable;

/**
 * 报纸视图对象 g_newspaper
 *
 * <AUTHOR>
 * @date 2024-03-30
 */
@Data
public class GNewspaperVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报纸id
     */
    private Long newspaperId;

    /**
     * 标题
     */
    private String newspaperTitle;

    /**
     * 分类
     */
    private Long newspaperType;

    /**
     * 报纸封面
     */
    private String newspaperCover;

    /**
     * 报纸地址
     */
    private String newspaperUrl;

    /**
     * 状态0下架 1上架
     */
    private Integer status;

    /**
     * 排序
     */
    private Long sort;


}
