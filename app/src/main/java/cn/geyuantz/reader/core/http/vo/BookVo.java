package cn.geyuantz.reader.core.http.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 图书视图对象 g_book
 *
 * <AUTHOR>
 * @date 2023-11-26
 */
@Data
public class BookVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图书id
     */
    private Long bookId;

    /**
     * 书名
     */
    private String bookName;

    /**
     * 出版社
     */
    private String publisher;

    /**
     * 作者
     */
    private String bookAuthor;

    /**
     * 封面
     */
    private String bookCover;

    /**
     * 图书默认地址
     */
    private String bookUrl;


}
