/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.core.http.bo;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 图书列表
 */
@Data
@Accessors(chain = true)
public class RAudioList {
    /**
     * 分类id
     */
    private Long classId;

    /**
     * 音频信息
     */
    private GAudioBo gAudioBo;

    /**
     * 分页
     */
    private PageQuery pageQuery;
}
