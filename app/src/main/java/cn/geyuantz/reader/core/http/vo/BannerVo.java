/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.core.http.vo;


import java.io.Serializable;

import lombok.Data;

@Data
public class BannerVo implements Serializable {
    /**
     * 图片名称
     */
    private String bannerName;
    /**
     * 图片链接地址
     */
    private String bannerUrl;
    /**
     * 图片是否可点击 0否1是
     */
    private Long bannerClickable;
    /**
     * 跳转方式 0内部模块1外链
     */
    private Long bannerJumpMethod;

    /**
     * 跳转模块id
     */
    private Long bannerJumpId;

    /**
     * 跳转模块实体
     */
    private ModuleVo moduleVo;

    /**
     * 跳转链接
     */
    private String bannerJumpHref;


}
