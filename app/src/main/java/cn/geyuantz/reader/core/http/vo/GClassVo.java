package cn.geyuantz.reader.core.http.vo;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * 资源分类视图对象 g_class
 *
 * <AUTHOR>
 * @date 2023-11-19
 */
@Data
public class GClassVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Long classId;

    /**
     * 父分类id
     */
    private Long parentId;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 状态
     */
    private Long status;


}
