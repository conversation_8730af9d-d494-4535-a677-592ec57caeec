/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.core.http;

import cn.geyuantz.reader.utils.TokenUtils;
import com.xuexiang.xhttp2.interceptor.BaseDynamicInterceptor;

import java.util.TreeMap;

import okhttp3.Request;

public class CustomDynamicInterceptor extends BaseDynamicInterceptor {
    @Override
    protected TreeMap<String, Object> updateDynamicParams(TreeMap dynamicMap) {
        return dynamicMap;
    }

    @Override
    protected Request.Builder updateHeaders(Request.Builder builder) {

        if (isAccessToken()) {
            //是否添加token
            builder.addHeader("Authorization", TokenUtils.getToken());
        }
        return builder;
    }
}
