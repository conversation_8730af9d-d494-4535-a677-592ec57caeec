package cn.geyuantz.reader.core.http.vo;

import java.io.Serializable;

import lombok.Data;


/**
 * 模块对象 g_module
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Data
public class ModuleVo implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 模块id
     */
    private Long moduleId;
    /**
     * 模块名称
     */
    private String moduleName;
    /**
     * 模块详情
     */
    private String moduleInfo;
    /**
     * 模块图标
     */
    private String moduleImg;
    /**
     * 模块类型 0定制模块 1.云图书 2.音频 3.视频
     */
    private Long moduleType;
    /**
     * 模块参数
     */
    private String moudleParams;
    /**
     * 模块归属 0.公共模块 1.机构定制模块
     */
    private String moduleOwner;
    /**
     * 是否支持安卓 0不支持 1支持
     */
    private Long moduleSupportsAndroid;
    /**
     * 模块是否支持windows  0不支持 1支持
     */
    private Long moduleSupportsWindows;
    /**
     * 是否支持离线 0.不支持 1.支持
     */
    private Long moduleSupportsOffline;
    /**
     * 排序
     */
    private Long sort;

}
