package cn.geyuantz.reader.core.http.vo;

import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * 音频视图对象 g_audio
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Data
public class GAudioVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 音频编号
     */
    private Long audioId;

    /**
     * 父编号
     */
    private Long parentId;

    /**
     * 音频名称
     */
    private String audioName;

    /**
     * 作者
     */
    private String audioAuthor;

    /**
     * 音频简介
     */
    private String audioInfo;

    /**
     * 浏览数
     */
    private Long audioVisitCount;

    /**
     * 封面
     */
    private String audioCover;

    /**
     * 音频路径
     */
    private String audioUrl;

    /**
     * 音频分级
     */
    private Long audioLevel;

    /**
     * 状态 0下架，1上架
     */
    private Long status;

    /**
     * 排序
     */
    private Long sort;


}
