/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.core.http.api;

import com.google.gson.JsonObject;
import cn.geyuantz.reader.core.http.bo.RBookList;
import cn.geyuantz.reader.core.http.bo.RRequestHelp;
import cn.geyuantz.reader.core.http.bo.RVideoList;
import cn.geyuantz.reader.core.http.entity.TipInfo;
import cn.geyuantz.reader.core.http.vo.BookVo;
import cn.geyuantz.reader.core.http.vo.DeviceInfoVo;
import cn.geyuantz.reader.core.http.vo.GAudioVo;
import cn.geyuantz.reader.core.http.vo.GClassVo;
import cn.geyuantz.reader.core.http.vo.GVideoVo;
import cn.geyuantz.reader.core.http.vo.LoginVo;
import cn.geyuantz.reader.core.http.bo.RAudioList;
import cn.geyuantz.reader.core.http.vo.NewsPaperListVo;
import cn.geyuantz.reader.core.http.vo.RobotDetailVo;
import com.xuexiang.xhttp2.annotation.NetMethod;
import com.xuexiang.xhttp2.model.ApiResult;

import java.util.List;

import io.reactivex.Observable;
import retrofit2.http.GET;

/**
 * <AUTHOR>
 * @since 2021/1/9 7:01 PM
 */
public class ApiService {

    /**
     * 使用的是retrofit的接口定义
     */
    public interface IGetService {

        /**
         * 获得小贴士
         */
        @GET("/xuexiangjys/Resource/raw/master/jsonapi/tips.json")
        Observable<ApiResult<List<TipInfo>>> getTips();
    }
    /**
     * 更新接口
     */
    public interface IUpdateService {
        /**
         * 检查更新
         */
        @NetMethod(url = "update/checkUpdate", action = NetMethod.GET,accessToken = false, parameterNames = {"softwareCode", "versionCode"})
        Observable<JsonObject> checkUpdate(String softwareCode, Integer versionCode);
    }

    /**
     * 登录接口
     */
    public interface IDeviceService {
        /**
         * 申请注册
         */
        @NetMethod(url = "device/apply", parameterNames = {"deviceCode", "softwareCode", "deviceRawCode", "remark", "deviceOrder"}, accessToken = false)
        Observable<String> apply(String deviceCode, String softwareCode, String deviceRawCode, String remark, String deviceOrder);

        /**
         * 请求协助
         */
        @NetMethod(url = "device/requestHelp", paramType = NetMethod.JSON_OBJECT)
        Observable<String> requestHelp(RRequestHelp rRequestHelp);

        /**
         * 登录
         *
         * @param deviceCode   设备码
         * @param softwareCode 软件码
         * @return
         */
        @NetMethod(url = "device/login", parameterNames = {"deviceCode", "softwareCode"}, accessToken = false)
        Observable<LoginVo> login(String deviceCode, String softwareCode);

        /**
         * 获取设备信息
         */
        @NetMethod(url = "device/info", accessToken = true)
        Observable<DeviceInfoVo> getDeviceInfo();

    }

    /**
     * 书籍接口
     */
    public interface IBookService {
        /**
         * 获取书籍列表
         */
        @NetMethod(url = "book/list", paramType = NetMethod.JSON_OBJECT)
        Observable<List<BookVo>> getBookList(RBookList rBookList);
    }

    /**
     * 分类接口
     */
    public interface IClassService {
        /**
         * 获取分类列表
         */
        @NetMethod(url = "class/list", parameterNames = {"classId"},action = NetMethod.GET)
        Observable<List<GClassVo>> getClassList(Long classId);
    }
    /**
     * 音频接口
     */
    public interface IAudioService {
        /**
         * 获取音频列表
         */
        @NetMethod(url = "audio/list", paramType = NetMethod.JSON_OBJECT)
        Observable<List<GAudioVo>> getAudioList(RAudioList rAudioList);
    }

    /**
     * 视频接口
     */
    public interface IVideoService {
        /**
         * 获取视频列表
         */
        @NetMethod(url = "video/list", paramType = NetMethod.JSON_OBJECT)
        Observable<List<GVideoVo>> getVideoList(RVideoList  rVideoList);
        /**
         * 获取视频播放列表
         */
        @NetMethod(url = "video/episode", parameterNames = {"videoId"},action = NetMethod.GET)
        Observable<List<GVideoVo>> getVideoPlayList(Long videoId);
    }
    /**
     * 报纸接口
     */
    public interface IPaperService {
        /**
         * 获取报纸列表
         */
        @NetMethod(url = "newspaper/list", action = NetMethod.GET)
        Observable<List<NewsPaperListVo>> getPaperList();
    }

    /**
     * AI机器人接口
     */
    public interface IRobotService {
        /**
         * 查询机器人详情信息
         */
        @NetMethod(url = "ai/robot", action = NetMethod.GET, parameterNames = {"robotId"})
        Observable<RobotDetailVo> getRobotDetail(Long robotId);
    }

}
