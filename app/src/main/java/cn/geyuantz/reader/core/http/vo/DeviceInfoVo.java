/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.core.http.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 设备信息
 */
@Data
public class DeviceInfoVo implements Serializable {
    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 设备标题
     */
    private String deviceTitle;

    /**
     * 机构图标
     */
    private String orgLogo;

    /**
     * 风格id
     */
    private Long styleId;
    /**
     * 背景图片
     */
    private String backgroundImg;

    /**
     * banner列表
     */
    private List<BannerVo> bannerList;

    /**
     * 模块列表
     */
    private List<ModuleVo> moduleList;


}
