package cn.geyuantz.reader.core.http.bo;


import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 图书业务对象 g_book
 *
 * <AUTHOR>
 * @date 2023-11-26
 */

@Data
public class GBookBo {

    /**
     * 图书id
     */
    private Long bookId;

    /**
     * 书名
     */
    private String bookName;

    /**
     * 简介
     */
    private String bookInfo;

    /**
     * 出版社
     */
    private String publisher;

    /**
     * 作者
     */
    private String bookAuthor;

    /**
     * 封面
     */
    private String bookCover;

    /**
     * pdf资源地址
     */
    private String bookUrlPdf;

    /**
     * epub资源地址
     */
    private String bookUrlEpub;

    /**
     * txt资源地址
     */
    private String bookUrlTxt;

    /**
     * 外链资源地址
     */
    private String bookUrlHref;

    /**
     * 图书默认地址
     */
    private String bookUrl;

    /**
     * 资源类型 1 pdf 、2 txt 、 3 epub 、 4 外链

     */
    private Integer bookType;

    /**
     * ISBN
     */
    private String bookIsbn;

    /**
     * 图书分级
     */
    private Integer bookLevel;

    /**
     * 浏览量
     */
    private Integer bookVisitCount;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;


}
