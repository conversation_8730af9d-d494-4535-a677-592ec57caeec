/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.utils.update;

import android.util.Log;

import androidx.annotation.NonNull;

import com.google.gson.JsonObject;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.subscriber.NoTipRequestSubscriber;
import com.xuexiang.xhttp2.XHttp;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xhttp2.XHttpSDK;
import com.xuexiang.xhttp2.callback.DownloadProgressCallBack;
import com.xuexiang.xhttp2.callback.SimpleCallBack;
import com.xuexiang.xhttp2.exception.ApiException;
import com.xuexiang.xui.utils.XToastUtils;
import com.xuexiang.xupdate.proxy.IUpdateHttpService;
import com.xuexiang.xutil.file.FileUtils;
import com.xuexiang.xutil.net.JsonUtil;

import java.util.Map;

/**
 * XHttp2实现的请求更新
 *
 * <AUTHOR>
 * @since 2018/8/12 上午11:46
 */
public class XHttpUpdateHttpServiceImpl implements IUpdateHttpService {

    @Override
    public void asyncGet(@NonNull String url, @NonNull Map<String, Object> params, @NonNull final IUpdateHttpService.Callback callBack) {
        XHttpProxy.proxy(ApiService.IUpdateService.class)
                .checkUpdate((String) params.get("softwareCode"), (Integer) params.get("versionCode"))
                .subscribe(new NoTipRequestSubscriber<JsonObject>() {
                    @Override
                    protected void onSuccess(JsonObject response) {
                        Log.d("XHttpUpdateHttpService", "onSuccess: " + response);
                        callBack.onSuccess(response.toString());
                    }
                });

        /*XHttp.get(url)
                .params(params)
//                .keepJson(true)
                .execute(new NoTipCallBack<String>() {
                    @Override
                    public void onSuccess(String response) throws Throwable {
                        //callBack.onSuccess(response);
                        Log.d("XHttpUpdateHttpService", "onSuccess: " + response);
                    }

                    @Override
                    public void onError(ApiException e) {
                        e.printStackTrace();
                    }
                });*/
      /* callBack.onSuccess("{\n" +
               "  \"Code\": 0, //0代表请求成功，非0代表失败\n" +
               "  \"Msg\": \"\", //请求出错的信息\n" +
               "  \"UpdateStatus\": 1, //0代表不更新，1代表有版本更新，不需要强制升级，2代表有版本更新，需要强制升级\n" +
               "  \"VersionCode\": 3,\n" +
               "  \"VersionName\": \"1.0.2\",\n" +
               "  \"ModifyContent\": \"1、优化api接口。\\r\\n2、添加使用demo演示。\\r\\n3、新增自定义更新服务API接口。\\r\\n4、优化更新提示界面。\",\n" +
               "  \"DownloadUrl\": \"https://raw.githubusercontent.com/xuexiangjys/XUpdate/master/apk/xupdate_demo_1.0.2.apk\",\n" +
               "  \"ApkSize\": 2048,\n" +
               "  \"ApkMd5\": \"...\"  //应用apk的md5值没有的话，就无法保证apk是否完整，每次都会重新下载。\n" +
               "}");*/
      /* callBack.onSuccess("{\n" +
               "  \"Code\": 0,\n" +
               "  \"Msg\": \"请求成功\",\n" +
               "  \"UpdateStatus\": 1,\n" +
               "  \"VersionCode\": 2,\n" +
               "  \"VersionName\": \"1.0.0\",\n" +
               "  \"ModifyContent\": \"优化部分功能\",\n" +
               "  \"DownloadUrl\": \"http://oss.geyuantz.cn/2024/07/13/bed15c39966f4af3a2c6525eabce85a0.apk\",\n" +
               "  \"ApkSize\": 30,\n" +
               "  " +
               "\"ApkMd5\": \"\"\n" +
               "}");*/
    }

    @Override
    public void asyncPost(@NonNull String url, @NonNull Map<String, Object> params, @NonNull final IUpdateHttpService.Callback callBack) {
        XHttp.post(url)
                .upJson(JsonUtil.toJson(params))
                .keepJson(true)
                .execute(new SimpleCallBack<String>() {
                    @Override
                    public void onSuccess(String response) throws Throwable {
                        callBack.onSuccess(response);
                    }

                    @Override
                    public void onError(ApiException e) {
                        callBack.onError(e);
                    }
                });
    }

    @Override
    public void download(@NonNull String url, @NonNull String path, @NonNull String fileName, @NonNull final IUpdateHttpService.DownloadCallback callback) {
        XHttpSDK.addRequest(url, XHttp.downLoad(url)
                .savePath(path)
                .saveName(fileName)
                .isUseBaseUrl(false)
                .execute(new DownloadProgressCallBack<String>() {
                    @Override
                    public void onStart() {
                        callback.onStart();
                    }

                    @Override
                    public void onError(ApiException e) {
                        callback.onError(e);
                    }

                    @Override
                    public void update(long downLoadSize, long totalSize, boolean done) {
                        callback.onProgress(downLoadSize / (float) totalSize, totalSize);
                    }

                    @Override
                    public void onComplete(String path) {
                        callback.onSuccess(FileUtils.getFileByPath(path));
                    }
                }));
    }

    @Override
    public void cancelDownload(@NonNull String url) {
        XToastUtils.info("已取消更新");
        XHttpSDK.cancelRequest(url);
    }
}
