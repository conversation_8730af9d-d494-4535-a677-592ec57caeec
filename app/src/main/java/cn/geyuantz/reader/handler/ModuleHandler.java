/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.handler;

import android.content.Intent;
import android.net.Uri;

import cn.geyuantz.reader.core.http.vo.GClassVo;
import cn.geyuantz.reader.core.http.vo.ModuleVo;
import cn.geyuantz.reader.fragment.ai.ChatbotFragment;
import cn.geyuantz.reader.fragment.audio.AudioCategoryFragment;
import cn.geyuantz.reader.fragment.book.BookListFragment;
import cn.geyuantz.reader.fragment.newsPaper.NewsPaperFragment;
import cn.geyuantz.reader.fragment.video.VideoCategoryFragment;
import cn.geyuantz.reader.fragment.video.VideoListFragment;
import cn.geyuantz.reader.utils.Utils;
import com.xuexiang.xpage.base.XPageFragment;
import com.xuexiang.xpage.core.PageOption;
import com.xuexiang.xui.utils.XToastUtils;

import java.io.Serializable;

/**
 * 模块处理器
 */
public class ModuleHandler
{
    private XPageFragment xPageFragment;

    public ModuleHandler(XPageFragment xPageFragment) {
        this.xPageFragment = xPageFragment;
    }

    public void handlerClick(ModuleVo moduleVo) {
        if (moduleVo.getModuleType() == 1) {
            //定制模块
            handlerCustomModule(moduleVo);
        } else if (moduleVo.getModuleType() == 2) {
            //外链
            Utils.goWeb(xPageFragment.getContext(), moduleVo.getMoudleParams());
        } else if (moduleVo.getModuleType() == 3) {
            //视频子模块
            String param = moduleVo.getMoudleParams().trim();
            GClassVo gClassVo = new GClassVo();
            gClassVo.setClassName(moduleVo.getModuleName());
            gClassVo.setClassId(Long.parseLong(param));
            openPage(VideoListFragment.class, "classVo", gClassVo);
        } else if (moduleVo.getModuleType()==4) {
            //跳转到其他app
            String param = moduleVo.getMoudleParams().trim();
            //判断是包名还是协议
            if (param.contains(":")) {
                try {
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(param));
                    xPageFragment.getContext().startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                Intent intent = xPageFragment.getContext().getPackageManager().getLaunchIntentForPackage(param);
                if (intent != null) {
                    xPageFragment.startActivity(intent);
                } else {
                    XToastUtils.toast("未安装该应用！");
                }
            }

        }else if (moduleVo.getModuleType()==5){
            //AI模块
            openPage(ChatbotFragment.class, "moduleVo", moduleVo);
        }
        else {
            XToastUtils.toast("暂不支持的模块类型！");
        }
    }

    //处理定制模块
    private void handlerCustomModule(ModuleVo moduleVo) {
        if (moduleVo.getModuleId() == 1) {//图书模块
            openPage(BookListFragment.class);
        } else if (moduleVo.getModuleId() == 2) {//音频模块
            openPage(AudioCategoryFragment.class);
        } else if (moduleVo.getModuleId() == 3) {//视频模块
            openPage(VideoCategoryFragment.class);
        }else if (moduleVo.getModuleId() == 4) {//报纸
            openPage(NewsPaperFragment.class);
        }
        else {
            XToastUtils.toast("暂不支持的模块！");
        }

    }

    //打开新的页面
    private void openPage(Class<? extends XPageFragment> clazz) {
        PageOption.to(clazz)
                .setNewActivity(true)
                .open(xPageFragment);
    }
    //打开新的页面 带参数
    private void openPage(Class<? extends XPageFragment> clazz, String paramName, Serializable paramValue) {
        PageOption.to(clazz)
                .setNewActivity(true)
                .putSerializable(paramName,paramValue)
                .open(xPageFragment);
    }
}
