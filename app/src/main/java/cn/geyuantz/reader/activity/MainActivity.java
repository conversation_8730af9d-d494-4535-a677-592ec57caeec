/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.activity;

import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import cn.geyuantz.reader.core.BaseActivity;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.subscriber.TipRequestSubscriber;
import cn.geyuantz.reader.core.http.vo.DeviceInfoVo;
import cn.geyuantz.reader.fragment.style.StandardMainFragment;
import cn.geyuantz.reader.fragment.style.GovMainFragment;
import cn.geyuantz.reader.utils.sdkinit.XUpdateInit;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xhttp2.exception.ApiException;
import com.xuexiang.xui.utils.XToastUtils;
import com.xuexiang.xutil.XUtil;
import com.xuexiang.xutil.common.ClickUtils;
import com.xuexiang.xutil.data.SPUtils;

/**
 * 登录页面
 *
 * <AUTHOR>
 * @since 2019-11-17 22:21
 */
public class MainActivity extends BaseActivity implements View.OnClickListener, BottomNavigationView.OnNavigationItemSelectedListener, ClickUtils.OnClick2ExitListener, Toolbar.OnMenuItemClickListener {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //检查风格并跳转
        checkStyleAndJump();

        //检查版本更新
        XUpdateInit.checkUpdate(getApplicationContext(), false);

    }

    @Override
    public void onClick(View view) {

    }

    /**
     * 检查风格并跳转
     */
    public void checkStyleAndJump() {
        //判断跳转风格
        XHttpProxy.proxy(ApiService.IDeviceService.class)
                .getDeviceInfo()
                .subscribe(new TipRequestSubscriber<DeviceInfoVo>() {
                    @Override
                    protected void onSuccess(DeviceInfoVo deviceInfoVo) {
                        Log.i("onSuccess: ", deviceInfoVo.toString());
                        Bundle bundle = new Bundle();
                        bundle.putSerializable("deviceInfo", deviceInfoVo);
                        //保存设备信息
                        SPUtils.putObject(SPUtils.getDefaultSharedPreferences(),"deviceInfo", deviceInfoVo);
                        if (deviceInfoVo.getStyleId() == 1) {
                            //跳转到标准版
                            openPage(StandardMainFragment.class, bundle);
                        } else if (deviceInfoVo.getStyleId() == 2) {
                            openPage(GovMainFragment.class, bundle);
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        super.onError(e);
                    }
                });
    }

    @Override
    public boolean onMenuItemClick(MenuItem item) {
        return false;
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        return false;
    }

    /**
     * 菜单、返回键响应
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            ClickUtils.exitBy2Click(2000, this);
        }
        return true;
    }

    @Override
    public void onRetry() {
        XToastUtils.toast("再按一次退出程序");
    }

    @Override
    public void onExit() {
        XUtil.exitApp();
    }



}
