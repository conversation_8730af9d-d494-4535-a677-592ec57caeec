/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.activity;

import android.util.Log;
import android.view.KeyEvent;

import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.vo.LoginVo;
import cn.geyuantz.reader.utils.TokenUtils;
import cn.geyuantz.reader.utils.Utils;
import cn.geyuantz.reader.MyApp;
import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.subscriber.TipRequestSubscriber;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xhttp2.exception.ApiException;
import com.xuexiang.xui.utils.KeyboardUtils;
import com.xuexiang.xui.widget.activity.BaseSplashActivity;
import com.xuexiang.xutil.app.ActivityUtils;

import me.jessyan.autosize.internal.CancelAdapt;

/**
 * 启动页【无需适配屏幕大小】
 *
 * <AUTHOR>
 * @since 2019-06-30 17:32
 */
public class SplashActivity extends BaseSplashActivity implements CancelAdapt {

    @Override
    protected long getSplashDurationMillis() {
        return 0; // 取消启动延迟，立即启动
    }

    /**
     * activity启动后的初始化
     */
    @Override
    protected void onCreateActivity() {
        // 显示启动画面，但保持快速启动
        startSplash(false);
    }

    @Override
    protected int getSplashImgResId() {
        return R.drawable.start_bg; // 使用start_bg.png作为启动背景
    }

    /**
     * 启动页结束后的动作
     */
    @Override
    protected void onSplashFinished() {
        //登录或者进入主页面
        login();
    }


    //=============================登录跳转===============================//
    private void login() {
        // 优化：使用异步方式进行登录，避免阻塞UI线程
        new Thread(() -> {
            try {
                XHttpProxy.proxy(ApiService.IDeviceService.class)
                        .login(Utils.getDeviceId(), MyApp.softwareCode)
                        .subscribe(new TipRequestSubscriber<LoginVo>() {

                            @Override
                            protected void onSuccess(LoginVo stringStringMap) {
                                Log.e("登录成功", stringStringMap.toString());
                                //储存token
                                TokenUtils.setToken(stringStringMap.getToken());
                                //登录成功，跳转到主页面
                                runOnUiThread(() -> {
                                    ActivityUtils.startActivity(MainActivity.class);
                                    finish();
                                });
                            }
                            @Override
                            public void onError(ApiException e) {
                                super.onError(e);
                                //登录失败，跳转到登录页面,并携带错误信息
                                runOnUiThread(() -> {
                                    ActivityUtils.startActivity(LoginActivity.class,"tip",e.getMessage());
                                    finish();
                                });
                            }
                        });
            } catch (Exception e) {
                e.printStackTrace();
                // 如果出现异常，直接跳转到登录页面
                runOnUiThread(() -> {
                    ActivityUtils.startActivity(LoginActivity.class);
                    finish();
                });
            }
        }).start();
    }

    /**
     * 菜单、返回键响应
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return KeyboardUtils.onDisableBackKeyDown(keyCode) && super.onKeyDown(keyCode, event);
    }
}
