/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.component;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.BannerVo;
import cn.geyuantz.reader.handler.ModuleHandler;
import cn.geyuantz.reader.utils.Utils;
import com.xuexiang.xpage.base.XPageFragment;
import com.xuexiang.xui.widget.banner.widget.banner.BannerItem;
import com.xuexiang.xui.widget.banner.widget.banner.base.BaseBanner;
import com.xuexiang.xui.widget.banner.widget.banner.base.BaseImageBanner;
import com.xuexiang.xui.widget.imageview.ImageLoader;
import com.xuexiang.xui.widget.imageview.strategy.DiskCacheStrategyEnum;
import com.xuexiang.xui.widget.imageview.strategy.LoadOption;

import java.util.ArrayList;
import java.util.List;

public class GyBanner extends BaseImageBanner<GyBanner> {


    public GyBanner(Context context) {
        super(context);
        init();
    }

    public GyBanner(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public GyBanner(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    /**
     * 初始化控件，设置圆角
     */
    private void init() {
        // 设置轻微圆角
        setBackground(getContext().getResources().getDrawable(R.drawable.banner_rounded_corners));
        // 设置圆角裁剪
        setClipToOutline(true);
    }

    @Override
    protected void loadingImageView(ImageView iv, BannerItem item) {
        String imgUrl = item.imgUrl;
        if (!TextUtils.isEmpty(imgUrl)) {
            // 设置 LayoutParams 为 match_parent
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.MATCH_PARENT
            );
            iv.setLayoutParams(layoutParams);
            iv.setScaleType(ImageView.ScaleType.FIT_XY);
            
            // 设置图片圆角
            iv.setClipToOutline(true);
            iv.setBackground(getContext().getResources().getDrawable(R.drawable.banner_rounded_corners));

            LoadOption option = LoadOption.of(mPlaceHolder)
                    .setSize (LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT)
                    .setCacheStrategy(mEnableCache ? DiskCacheStrategyEnum.RESOURCE : DiskCacheStrategyEnum.NONE);
            ImageLoader.get().loadImage(iv, imgUrl, option);
        } else {
            iv.setImageDrawable(mPlaceHolder);
        }
    }

    @Override
    protected int getItemLayoutId() {
        return R.layout.xui_adapter_simple_image;
    }

    @Override
    protected int getImageViewId() {
        return R.id.iv;
    }

    @Override
    public GyBanner setSource(List<BannerItem> list) {
        return super.setSource(list);
    }

    /**
     * 处理格源的banner数据，
     *
     * @param list banner数据
     */
    public void initGyBanner(List<BannerVo> list, XPageFragment xPageFragment) {
        List<BannerItem> bannerItemList = new ArrayList<>();
        for (BannerVo banner : list) {
            BannerItem item = new BannerItem();
            item.imgUrl = banner.getBannerUrl();
            item.title = banner.getBannerName();
            bannerItemList.add(item);
        }
        setSource(bannerItemList)
                .setOnItemClickListener(new BaseBanner.OnItemClickListener<BannerItem>() {
                    @Override
                    public void onItemClick(View view, BannerItem item, int position) {
                        BannerVo bannerVo = list.get(position);
                        //判断是否可点击
                        if (bannerVo.getBannerClickable() == 1) {
                            //判断跳转方式
                            if (bannerVo.getBannerJumpMethod() == 0) {
                                //内部模块
                                ModuleHandler moduleHandler = new ModuleHandler(xPageFragment);
                                moduleHandler.handlerClick(bannerVo.getModuleVo());
                            } else if (bannerVo.getBannerJumpMethod() == 1) {
                                //外链
                                Utils.goWeb(getContext(), bannerVo.getBannerJumpHref());
                            }
                        }
                    }
                }).startScroll();
    }
}

