package cn.geyuantz.reader.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import pl.droidsonroids.gif.GifImageView;
import pl.droidsonroids.gif.GifDrawable;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.GAudioVo;

public class ModernAudioPlayer extends LinearLayout {

    private GifImageView gifAudioCover;
    private TextView tvAudioTitle;
    private SeekBar seekbarProgress;
    private TextView tvCurrentTime;
    private TextView tvTotalTime;
    private ImageButton btnPrevious;
    private ImageButton btnPlayPause;
    private ImageButton btnNext;

    private boolean isPlaying = false;
    private GAudioVo currentAudio;
    private OnAudioControlListener listener;
    private int currentPosition = 0;
    private int duration = 0;

    public interface OnAudioControlListener {
        void onPlayPause(boolean isPlaying);
        void onPrevious();
        void onNext();
        void onSeekTo(int position);
    }

    public ModernAudioPlayer(Context context) {
        super(context);
        init();
    }

    public ModernAudioPlayer(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ModernAudioPlayer(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        LayoutInflater.from(getContext()).inflate(R.layout.layout_modern_audio_player, this, true);
        
        initViews();
        setupListeners();
    }

    private void initViews() {
        gifAudioCover = findViewById(R.id.gif_audio_cover);
        tvAudioTitle = findViewById(R.id.tv_audio_title);
        seekbarProgress = findViewById(R.id.seekbar_progress);
        tvCurrentTime = findViewById(R.id.tv_current_time);
        tvTotalTime = findViewById(R.id.tv_total_time);
        btnPrevious = findViewById(R.id.btn_previous);
        btnPlayPause = findViewById(R.id.btn_play_pause);
        btnNext = findViewById(R.id.btn_next);

        // 初始化时停止GIF动画，显示第一帧
        if (gifAudioCover != null) {
            gifAudioCover.setImageResource(R.drawable.music);
            // 确保初始状态为静态
            post(() -> {
                if (gifAudioCover.getDrawable() instanceof GifDrawable) {
                    ((GifDrawable) gifAudioCover.getDrawable()).stop();
                }
            });
        }
    }

    private void setupListeners() {
        btnPlayPause.setOnClickListener(v -> {
            isPlaying = !isPlaying;
            updatePlayPauseButton();
            if (listener != null) {
                listener.onPlayPause(isPlaying);
            }
        });

        btnPrevious.setOnClickListener(v -> {
            if (listener != null) {
                listener.onPrevious();
            }
        });

        btnNext.setOnClickListener(v -> {
            if (listener != null) {
                listener.onNext();
            }
        });

        seekbarProgress.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser && listener != null) {
                    int seekPosition = (int) ((progress / 100.0f) * duration);
                    listener.onSeekTo(seekPosition);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
    }

    public void setAudioInfo(GAudioVo audio) {
        this.currentAudio = audio;
        if (audio != null) {
            tvAudioTitle.setText(audio.getAudioName());
            // 这里可以加载音频封面图片
            // Glide.with(getContext()).load(audio.getCoverUrl()).into(ivAudioCover);
        }
    }

    public void setPlayState(boolean playing) {
        this.isPlaying = playing;
        updatePlayPauseButton();
        updateGifAnimation();
    }

    private void updatePlayPauseButton() {
        btnPlayPause.setImageResource(isPlaying ? R.drawable.ic_pause_large : R.drawable.ic_play_arrow_large);
    }

    /**
     * 更新GIF动画状态
     */
    private void updateGifAnimation() {
        if (gifAudioCover != null) {
            if (isPlaying) {
                // 开始播放GIF动画
                startGifAnimation();
            } else {
                // 停止播放GIF动画
                stopGifAnimation();
            }
        }
    }

    /**
     * 开始播放GIF动画
     */
    private void startGifAnimation() {
        if (gifAudioCover != null) {
            // 确保GIF资源已加载
            gifAudioCover.setImageResource(R.drawable.music);
            // 开始播放GIF动画
            if (gifAudioCover.getDrawable() instanceof GifDrawable) {
                ((GifDrawable) gifAudioCover.getDrawable()).start();
            }
        }
    }

    /**
     * 停止播放GIF动画，显示第一帧
     */
    private void stopGifAnimation() {
        if (gifAudioCover != null) {
            // 停止GIF动画，显示第一帧
            if (gifAudioCover.getDrawable() instanceof GifDrawable) {
                ((GifDrawable) gifAudioCover.getDrawable()).stop();
            }
        }
    }

    public void updateProgress(int currentPosition, int duration) {
        this.currentPosition = currentPosition;
        this.duration = duration;

        if (duration > 0) {
            int progress = (int) ((currentPosition * 100.0f) / duration);
            seekbarProgress.setProgress(progress);
        }

        tvCurrentTime.setText(formatTime(currentPosition));
        tvTotalTime.setText(formatTime(duration));
    }

    private String formatTime(int milliseconds) {
        int seconds = milliseconds / 1000;
        int minutes = seconds / 60;
        seconds = seconds % 60;
        return String.format("%02d:%02d", minutes, seconds);
    }

    public void setOnAudioControlListener(OnAudioControlListener listener) {
        this.listener = listener;
    }
}
