package cn.geyuantz.reader.adapter.entity;

import cn.jiguang.imui.commons.models.IUser;

public class User implements IUser {

    // 用户id
    private String id;
    // 用户昵称
    private String displayName;
    // 用户头像
    private String avatar;

    public User(String id, String displayName, String avatar) {
        this.id = id;
        this.displayName = displayName;
        this.avatar = avatar;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String getAvatarFilePath() {
        return avatar;
    }
}