package cn.geyuantz.reader.adapter.reader;

import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.GClassVo;
import com.xuexiang.xui.adapter.recyclerview.BaseRecyclerAdapter;
import com.xuexiang.xui.adapter.recyclerview.RecyclerViewHolder;

import java.util.Random;

public class ModernCategoryAdapter extends BaseRecyclerAdapter<GClassVo> {

    // 预定义的渐变色彩组合
    private final String[][] gradientColors = {
        {"#667eea", "#764ba2"}, // 紫蓝渐变
        {"#f093fb", "#f5576c"}, // 粉红渐变
        {"#4facfe", "#00f2fe"}, // 蓝青渐变
        {"#43e97b", "#38f9d7"}, // 绿青渐变
        {"#fa709a", "#fee140"}, // 粉黄渐变
        {"#a8edea", "#fed6e3"}, // 青粉渐变
        {"#ff9a9e", "#fecfef"}, // 粉色渐变
        {"#a18cd1", "#fbc2eb"}  // 紫粉渐变
    };



    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.item_video_category_modern;
    }

    @Override
    protected void bindData(@NonNull RecyclerViewHolder holder, int position, GClassVo item) {
        if (item != null) {
            TextView tvCategoryName = holder.findViewById(R.id.tv_category_name);
            ImageView ivCategoryIcon = holder.findViewById(R.id.iv_category_icon);
            View iconBackground = holder.findViewById(R.id.icon_background);

            // 设置分类名称
            tvCategoryName.setText(item.getClassName());

            // 设置图标背景渐变色（根据位置选择不同颜色）
            String[] colors = gradientColors[position % gradientColors.length];
            setGradientBackground(iconBackground, colors[0], colors[1]);

            // 设置图标（可以根据分类类型设置不同图标）
            setIconByCategory(ivCategoryIcon, item.getClassName());
        }
    }

    /**
     * 设置渐变背景
     */
    private void setGradientBackground(View view, String startColor, String endColor) {
        GradientDrawable gradientDrawable = new GradientDrawable();
        gradientDrawable.setShape(GradientDrawable.OVAL);
        gradientDrawable.setColors(new int[]{
            Color.parseColor(startColor),
            Color.parseColor(endColor)
        });
        gradientDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
        gradientDrawable.setOrientation(GradientDrawable.Orientation.TL_BR);
        view.setBackground(gradientDrawable);
    }

    /**
     * 根据分类名称设置对应图标
     */
    private void setIconByCategory(ImageView imageView, String categoryName) {
        int iconRes = R.drawable.ic_video_category; // 默认图标

        if (categoryName != null) {
            String name = categoryName.toLowerCase();
            if (name.contains("电影") || name.contains("movie")) {
                iconRes = R.drawable.ic_video_category;
            } else if (name.contains("电视") || name.contains("tv") || name.contains("剧")) {
                iconRes = R.drawable.ic_video_category;
            } else if (name.contains("动漫") || name.contains("动画")) {
                iconRes = R.drawable.ic_video_category;
            } else if (name.contains("综艺") || name.contains("娱乐")) {
                iconRes = R.drawable.ic_video_category;
            } else if (name.contains("纪录") || name.contains("documentary")) {
                iconRes = R.drawable.ic_video_category;
            } else if (name.contains("音乐") || name.contains("music")) {
                iconRes = R.drawable.ic_video_category;
            }
        }

        imageView.setImageResource(iconRes);
    }
}
