package cn.geyuantz.reader.adapter.reader;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.GVideoVo;
import com.xuexiang.xui.adapter.recyclerview.BaseRecyclerAdapter;
import com.xuexiang.xui.adapter.recyclerview.RecyclerViewHolder;

public class ModernVideoPlayListAdapter extends BaseRecyclerAdapter<GVideoVo> {

    private int currentPlayingPosition = -1; // 当前播放的位置

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.item_video_episode_modern;
    }

    @Override
    protected void bindData(@NonNull RecyclerViewHolder holder, int position, GVideoVo item) {
        if (item != null) {
            // 集数编号
            TextView tvEpisodeNumber = holder.findViewById(R.id.tv_episode_number);
            tvEpisodeNumber.setText(String.format("%02d", position + 1));

            // 视频标题
            TextView tvTitle = holder.findViewById(R.id.tv_title);
            tvTitle.setText(item.getVideoName());

            // 播放状态指示器
            ImageView ivPlayStatus = holder.findViewById(R.id.iv_play_status);
            if (position == currentPlayingPosition) {
                ivPlayStatus.setVisibility(View.VISIBLE);
                // 可以添加高亮效果
                holder.itemView.setAlpha(1.0f);
            } else {
                ivPlayStatus.setVisibility(View.GONE);
                holder.itemView.setAlpha(0.8f);
            }
        }
    }

    /**
     * 设置当前播放的位置
     */
    public void setCurrentPlayingPosition(int position) {
        int oldPosition = currentPlayingPosition;
        currentPlayingPosition = position;
        
        // 刷新旧位置和新位置的显示
        if (oldPosition != -1) {
            notifyItemChanged(oldPosition);
        }
        if (position != -1) {
            notifyItemChanged(position);
        }
    }


}
