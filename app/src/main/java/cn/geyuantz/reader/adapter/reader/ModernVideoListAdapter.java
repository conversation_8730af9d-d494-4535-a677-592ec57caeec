package cn.geyuantz.reader.adapter.reader;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.GVideoVo;
import com.xuexiang.xui.adapter.recyclerview.BaseRecyclerAdapter;
import com.xuexiang.xui.adapter.recyclerview.RecyclerViewHolder;

public class ModernVideoListAdapter extends BaseRecyclerAdapter<GVideoVo> {

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.item_video_list_modern;
    }

    @Override
    protected void bindData(@NonNull RecyclerViewHolder holder, int position, GVideoVo item) {
        if (item != null) {
            // 视频标题
            TextView tvTitle = holder.findViewById(R.id.tv_title);
            tvTitle.setText(item.getVideoName());

            // 视频数量 - 显示静态数据
            TextView tvVideoCount = holder.findViewById(R.id.tv_video_count);
            tvVideoCount.setText("共15个视频");

            // 封面图片
            ImageView ivCover = holder.findViewById(R.id.iv_cover);
            loadCoverImage(ivCover, item.getVideoCover());
        }
    }

    /**
     * 加载封面图片
     */
    private void loadCoverImage(ImageView imageView, String coverUrl) {
        RequestOptions options = new RequestOptions()
                .placeholder(R.drawable.ic_logo_app)
                .error(R.drawable.ic_logo_app)
                .fitCenter() // 使用fitCenter拉伸铺满，不裁剪
                .transform(new RoundedCorners(36)); // 12dp圆角

        if (!TextUtils.isEmpty(coverUrl)) {
            Glide.with(imageView.getContext())
                    .load(coverUrl)
                    .apply(options)
                    .into(imageView);
        } else {
            Glide.with(imageView.getContext())
                    .load(R.drawable.ic_logo_app)
                    .apply(options)
                    .into(imageView);
        }
    }

    /**
     * 格式化观看次数显示
     */
    private String formatViewCount(Long count) {
        if (count == null || count <= 0) {
            return "0";
        }
        
        if (count < 1000) {
            return count.toString();
        } else if (count < 10000) {
            return String.format("%.1fK", count / 1000.0);
        } else if (count < 100000000) {
            return String.format("%.1f万", count / 10000.0);
        } else {
            return String.format("%.1f亿", count / 100000000.0);
        }
    }
}
