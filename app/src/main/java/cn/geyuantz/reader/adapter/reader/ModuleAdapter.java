/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.adapter.reader;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.ModuleVo;
import cn.geyuantz.reader.handler.ModuleHandler;
import com.xuexiang.xpage.base.XPageFragment;
import com.xuexiang.xui.widget.imageview.ImageLoader;
import com.xuexiang.xui.widget.imageview.RadiusImageView;

import java.util.List;

import me.jessyan.autosize.utils.AutoSizeUtils;

public class ModuleAdapter extends BaseAdapter {
    /**
     * 模块列表
     */
    private List<ModuleVo> mMoudleList;
    /**
     * 文字颜色
     */
    private int textColor;
    /**
     * 模块宽度
     */
    private Integer mModuleWidth;
    /**
     * 模块高度
     */
    private Integer mModuleHeight;

    /**
     * 当前页面
     */
    private XPageFragment xPageFragment;


    /**
     * 构造方法
     *
     * @param xPageFragment 当前页面
     * @param moduleList   模块列表
     * @param textColor   文字颜色
     */
    public ModuleAdapter(XPageFragment xPageFragment, List<ModuleVo> moduleList, int textColor) {

        mMoudleList = moduleList;
        this.textColor = textColor;
        this.xPageFragment = xPageFragment;
    }

    /**
     * 构造方法,可传入模块大小
     *
     */
    public ModuleAdapter(XPageFragment xPageFragment, List<ModuleVo> moduleList, int textColor, int moduleWidth, int moduleHeight) {
        mMoudleList = moduleList;
        this.textColor = textColor;
        this.mModuleWidth = moduleWidth;
        this.mModuleHeight = moduleHeight;
        this.xPageFragment = xPageFragment;
    }
    /**
     * 设置模块宽高
     */
    public void setModuleSize(int moduleWidth, int moduleHeight) {
        this.mModuleWidth = moduleWidth;
        this.mModuleHeight = moduleHeight;
    }



    @Override
    public int getCount() {
        return mMoudleList.size();
    }

    @Override
    public Object getItem(int i) {
        return mMoudleList.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        ModuleVo item = mMoudleList.get(i);
        View gridItem = LayoutInflater.from(xPageFragment.getContext()).inflate(R.layout.adapter_common_grid_item, null);
        if (mModuleWidth != null && mModuleHeight != null) {
            gridItem.setLayoutParams(new ViewGroup.LayoutParams(AutoSizeUtils.dp2px( xPageFragment.getContext(),mModuleWidth),  AutoSizeUtils.dp2px( xPageFragment.getContext(),mModuleHeight)));
        }
        RadiusImageView radiusImageView = gridItem.findViewById(R.id.riv_item);
        TextView textView = gridItem.findViewById(R.id.tv_sub_title);
        textView.setText(item.getModuleName());
        // 强制设置为深红色，覆盖其他设置
        textView.setTextColor(android.graphics.Color.rgb(139, 0, 0));
        ImageLoader.get().loadImage(radiusImageView, item.getModuleImg());
        ModuleHandler moduleHandler = new ModuleHandler(xPageFragment);
        gridItem.setOnClickListener(v -> {
            moduleHandler.handlerClick(item);
        });
        return gridItem;
    }




}
