package cn.geyuantz.reader.adapter.reader;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import pl.droidsonroids.gif.GifImageView;
import pl.droidsonroids.gif.GifDrawable;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.GAudioVo;
import com.xuexiang.xui.adapter.recyclerview.BaseRecyclerAdapter;
import com.xuexiang.xui.adapter.recyclerview.RecyclerViewHolder;

public class ModernAudioListAdapter extends BaseRecyclerAdapter<GAudioVo> {

    private int currentPlayingPosition = -1; // 当前播放的位置
    private RecyclerView recyclerView; // RecyclerView引用，用于滚动
    private boolean isPlaying = false; // 当前播放状态

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.item_audio_episode_modern;
    }

    @Override
    protected void bindData(@NonNull RecyclerViewHolder holder, int position, GAudioVo item) {
        if (item != null) {
            // 集数编号
            TextView tvEpisodeNumber = holder.findViewById(R.id.tv_episode_number);
            tvEpisodeNumber.setText(String.format("%02d", position + 1));

            // 音频标题
            TextView tvTitle = holder.findViewById(R.id.tv_title);
            tvTitle.setText(item.getAudioName());

            // 内容容器
            LinearLayout llContent = holder.findViewById(R.id.ll_content);

            // 播放状态指示器 (GIF)
            GifImageView gifPlayStatus = holder.findViewById(R.id.gif_play_status);

            // 设置播放状态
            boolean isCurrentPlaying = position == currentPlayingPosition;
            if (isCurrentPlaying) {
                gifPlayStatus.setVisibility(View.VISIBLE);
                // 根据播放状态控制GIF动画
                if (isPlaying) {
                    // 播放时启动GIF动画
                    if (gifPlayStatus.getDrawable() instanceof GifDrawable) {
                        ((GifDrawable) gifPlayStatus.getDrawable()).start();
                    }
                } else {
                    // 暂停时停止GIF动画，显示第一帧
                    if (gifPlayStatus.getDrawable() instanceof GifDrawable) {
                        ((GifDrawable) gifPlayStatus.getDrawable()).stop();
                    }
                }
                // 设置选中状态
                llContent.setActivated(true);
                tvTitle.setActivated(true);
                holder.itemView.setAlpha(1.0f);
            } else {
                gifPlayStatus.setVisibility(View.GONE);
                // 取消选中状态
                llContent.setActivated(false);
                tvTitle.setActivated(false);
                holder.itemView.setAlpha(0.9f);
            }
        }
    }



    /**
     * 设置RecyclerView引用
     */
    public void setRecyclerView(RecyclerView recyclerView) {
        this.recyclerView = recyclerView;
    }

    /**
     * 设置当前播放位置
     */
    public void setCurrentPlayingPosition(int position) {
        int oldPosition = currentPlayingPosition;
        currentPlayingPosition = position;

        // 刷新旧位置和新位置的item
        if (oldPosition != -1) {
            notifyItemChanged(oldPosition);
        }
        if (position != -1) {
            notifyItemChanged(position);
            // 滚动到当前播放位置（不强制到顶部）
            scrollToPosition(position);
        }
    }

    /**
     * 获取当前播放位置
     */
    public int getCurrentPlayingPosition() {
        return currentPlayingPosition;
    }



    /**
     * 设置播放状态
     */
    public void setPlayingState(boolean playing) {
        boolean oldPlaying = isPlaying;
        isPlaying = playing;

        // 如果播放状态改变，刷新当前播放项
        if (oldPlaying != playing && currentPlayingPosition != -1) {
            notifyItemChanged(currentPlayingPosition);
        }
    }

    /**
     * 清除播放状态
     */
    public void clearPlayingState() {
        setCurrentPlayingPosition(-1);
    }

    /**
     * 滚动到指定位置（简单滚动，不强制到顶部）
     */
    private void scrollToPosition(int position) {
        if (recyclerView != null && position >= 0 && position < getItemCount()) {
            // 使用平滑滚动到指定位置，让系统决定最佳滚动位置
            recyclerView.post(() -> {
                recyclerView.smoothScrollToPosition(position);
            });
        }
    }
}
