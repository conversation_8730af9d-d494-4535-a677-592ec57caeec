package cn.geyuantz.reader.adapter.reader;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.GClassVo;

public class CategoryBubbleAdapter extends RecyclerView.Adapter<CategoryBubbleAdapter.ViewHolder> {

    private List<GClassVo> mCategories = new ArrayList<>();
    private int mSelectedPosition = 0; // 默认选中第一个
    private OnCategoryClickListener mOnCategoryClickListener;

    public interface OnCategoryClickListener {
        void onCategoryClick(GClassVo category, int position);
    }

    public void setOnCategoryClickListener(OnCategoryClickListener listener) {
        this.mOnCategoryClickListener = listener;
    }

    public void setCategories(List<GClassVo> categories) {
        this.mCategories.clear();
        if (categories != null) {
            this.mCategories.addAll(categories);
        }
        notifyDataSetChanged();
    }

    public void setSelectedPosition(int position) {
        int oldPosition = mSelectedPosition;
        mSelectedPosition = position;
        notifyItemChanged(oldPosition);
        notifyItemChanged(mSelectedPosition);
    }

    public int getSelectedPosition() {
        return mSelectedPosition;
    }

    public GClassVo getSelectedCategory() {
        if (mSelectedPosition >= 0 && mSelectedPosition < mCategories.size()) {
            return mCategories.get(mSelectedPosition);
        }
        return null;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_category_bubble, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        GClassVo category = mCategories.get(position);
        holder.tvCategory.setText(category.getClassName());
        holder.tvCategory.setSelected(position == mSelectedPosition);
        
        holder.itemView.setOnClickListener(v -> {
            if (mOnCategoryClickListener != null) {
                setSelectedPosition(position);
                mOnCategoryClickListener.onCategoryClick(category, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mCategories.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvCategory;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCategory = itemView.findViewById(R.id.tv_category);
        }
    }
}
