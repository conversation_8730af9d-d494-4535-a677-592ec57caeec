/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.ModuleVo;
import com.xuexiang.xpage.base.XPageFragment;

import java.util.ArrayList;
import java.util.List;

import me.jessyan.autosize.utils.AutoSizeUtils;
import android.graphics.Rect;

/**
 * 模块GridView翻页适配器
 */
public class ModuleGridPagerAdapter extends RecyclerView.Adapter<ModuleGridPagerAdapter.GridPageViewHolder> {

    private List<List<ModuleVo>> mPageList;
    private XPageFragment mFragment;
    private static final int MODULES_PER_PAGE = 8; // 每页8个模块（2行4列）

    public ModuleGridPagerAdapter(List<ModuleVo> moduleList, XPageFragment fragment) {
        this.mFragment = fragment;
        this.mPageList = splitIntoPages(moduleList);
    }
    
    /**
     * 将模块列表分页，每页8个模块
     */
    private List<List<ModuleVo>> splitIntoPages(List<ModuleVo> moduleList) {
        List<List<ModuleVo>> pages = new ArrayList<>();
        if (moduleList == null || moduleList.isEmpty()) {
            return pages;
        }
        
        for (int i = 0; i < moduleList.size(); i += MODULES_PER_PAGE) {
            int endIndex = Math.min(i + MODULES_PER_PAGE, moduleList.size());
            pages.add(new ArrayList<>(moduleList.subList(i, endIndex)));

        }
        
        return pages;
    }
    
    @NonNull
    @Override
    public GridPageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_module_grid_page, parent, false);
        return new GridPageViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull GridPageViewHolder holder, int position) {
        List<ModuleVo> pageModules = mPageList.get(position);
        holder.bindData(pageModules, mFragment);
    }
    
    @Override
    public int getItemCount() {
        return mPageList.size();
    }
    
    static class GridPageViewHolder extends RecyclerView.ViewHolder {
        private RecyclerView recyclerView;

        public GridPageViewHolder(@NonNull View itemView) {
            super(itemView);
            recyclerView = itemView.findViewById(R.id.rv_modules);
            setupRecyclerView();
        }
        
        private void setupRecyclerView() {
            // 设置RecyclerView为网格布局，4列
            GridLayoutManager layoutManager = new GridLayoutManager(itemView.getContext(), 4);
            recyclerView.setLayoutManager(layoutManager);

            // 减少间距，给模块更多宽度
            int spacing = AutoSizeUtils.dp2px(itemView.getContext(), 6);
            recyclerView.addItemDecoration(new GridSpacingItemDecoration(4, spacing, true));

            // 禁用滚动，因为我们使用ViewPager2来翻页
            recyclerView.setNestedScrollingEnabled(false);

        }
        
        public void bindData(List<ModuleVo> modules, XPageFragment fragment) {
            ModuleRecyclerAdapter adapter = new ModuleRecyclerAdapter(modules, fragment);
            recyclerView.setAdapter(adapter);
        }
    }

    /**
     * RecyclerView网格间距装饰器
     */
    static class GridSpacingItemDecoration extends RecyclerView.ItemDecoration {
        private int spanCount;
        private int spacing;
        private boolean includeEdge;

        public GridSpacingItemDecoration(int spanCount, int spacing, boolean includeEdge) {
            this.spanCount = spanCount;
            this.spacing = spacing;
            this.includeEdge = includeEdge;
        }

        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            int position = parent.getChildAdapterPosition(view);
            int column = position % spanCount;

            if (includeEdge) {
                outRect.left = spacing - column * spacing / spanCount;
                outRect.right = (column + 1) * spacing / spanCount;

                if (position < spanCount) {
                    outRect.top = spacing;
                }
                outRect.bottom = spacing;
            } else {
                outRect.left = column * spacing / spanCount;
                outRect.right = spacing - (column + 1) * spacing / spanCount;
                if (position >= spanCount) {
                    outRect.top = spacing;
                }
            }
        }
    }
}
