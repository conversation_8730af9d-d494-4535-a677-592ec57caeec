/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.adapter.reader;

import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.BookVo;
import com.xuexiang.xui.adapter.recyclerview.BaseRecyclerAdapter;
import com.xuexiang.xui.adapter.recyclerview.RecyclerViewHolder;
import com.xuexiang.xui.widget.imageview.preview.loader.GlideMediaLoader;

public class BookAdapter extends BaseRecyclerAdapter<BookVo> {

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.adapter_item_book_preview;
    }

    @Override
    protected void bindData(@NonNull RecyclerViewHolder holder, int position, BookVo item) {
        if (item != null) {
            ImageView ivCover = holder.findViewById(R.id.iv_cover);
            TextView tvTitle = holder.findViewById(R.id.tv_title);
            Glide.with(ivCover.getContext())
                    .load(item.getBookCover())
                    .apply(GlideMediaLoader.getRequestOptions())
                    .into(ivCover);
            tvTitle.setText(item.getBookName());
        }
    }
}
