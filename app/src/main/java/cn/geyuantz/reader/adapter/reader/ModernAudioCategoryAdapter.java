package cn.geyuantz.reader.adapter.reader;

import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.GClassVo;
import com.xuexiang.xui.adapter.recyclerview.BaseRecyclerAdapter;
import com.xuexiang.xui.adapter.recyclerview.RecyclerViewHolder;

public class ModernAudioCategoryAdapter extends BaseRecyclerAdapter<GClassVo> {

    // 预定义的音频主题渐变色彩组合 - 更加鲜艳的颜色
    private final String[][] gradientColors = {
        {"#FF6B6B", "#4ECDC4"}, // 红青渐变 - 音乐
        {"#A8E6CF", "#FFD93D"}, // 绿黄渐变 - 播客
        {"#FF8A80", "#FF7043"}, // 橙红渐变 - 有声书
        {"#9C27B0", "#E91E63"}, // 紫粉渐变 - 广播
        {"#2196F3", "#21CBF3"}, // 蓝色渐变 - 新闻
        {"#FFC107", "#FF9800"}, // 黄橙渐变 - 教育
        {"#E91E63", "#F06292"}, // 粉红渐变 - 娱乐
        {"#673AB7", "#9C27B0"}  // 深紫渐变 - 其他
    };

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.item_video_category_modern;
    }

    @Override
    protected void bindData(@NonNull RecyclerViewHolder holder, int position, GClassVo item) {
        if (item != null) {
            TextView tvCategoryName = holder.findViewById(R.id.tv_category_name);
            ImageView ivCategoryIcon = holder.findViewById(R.id.iv_category_icon);
            View iconBackground = holder.findViewById(R.id.icon_background);

            // 设置分类名称
            tvCategoryName.setText(item.getClassName());

            // 设置图标背景渐变色（根据位置选择不同颜色）
            String[] colors = gradientColors[position % gradientColors.length];
            setGradientBackground(iconBackground, colors[0], colors[1]);

            // 设置音频相关图标
            setAudioIconByCategory(ivCategoryIcon, item.getClassName());
        }
    }

    /**
     * 设置渐变背景
     */
    private void setGradientBackground(View view, String startColor, String endColor) {
        GradientDrawable gradientDrawable = new GradientDrawable();
        gradientDrawable.setShape(GradientDrawable.OVAL);
        gradientDrawable.setColors(new int[]{
            Color.parseColor(startColor),
            Color.parseColor(endColor)
        });
        gradientDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
        gradientDrawable.setOrientation(GradientDrawable.Orientation.TL_BR);
        view.setBackground(gradientDrawable);
    }

    /**
     * 根据音频分类名称设置对应图标
     */
    private void setAudioIconByCategory(ImageView imageView, String categoryName) {
        int iconRes = R.drawable.ic_audio_category; // 默认音频图标

        if (categoryName != null) {
            String name = categoryName.toLowerCase();
            if (name.contains("音乐") || name.contains("music") || name.contains("歌曲")) {
                iconRes = R.drawable.ic_audio_category;
            } else if (name.contains("播客") || name.contains("podcast")) {
                iconRes = R.drawable.ic_audio_category;
            } else if (name.contains("有声书") || name.contains("audiobook") || name.contains("小说")) {
                iconRes = R.drawable.ic_audio_category;
            } else if (name.contains("广播") || name.contains("radio")) {
                iconRes = R.drawable.ic_audio_category;
            } else if (name.contains("新闻") || name.contains("news")) {
                iconRes = R.drawable.ic_audio_category;
            } else if (name.contains("教育") || name.contains("education") || name.contains("学习")) {
                iconRes = R.drawable.ic_audio_category;
            } else if (name.contains("娱乐") || name.contains("entertainment")) {
                iconRes = R.drawable.ic_audio_category;
            }
        }

        imageView.setImageResource(iconRes);
    }
}
