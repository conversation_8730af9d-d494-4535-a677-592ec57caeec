/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.adapter.reader;

import android.widget.TextView;

import androidx.annotation.NonNull;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.GClassVo;
import com.xuexiang.xui.adapter.recyclerview.BaseRecyclerAdapter;
import com.xuexiang.xui.adapter.recyclerview.RecyclerViewHolder;

public class CategoryAdapter extends BaseRecyclerAdapter<GClassVo> {

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.item_category;
    }



    @Override
    protected void bindData(@NonNull RecyclerViewHolder holder, int position, GClassVo item) {
        if (item != null) {
            TextView tvTitle = holder.findViewById(R.id.tv_title);
            tvTitle.setText(item.getClassName());
        }
    }
}
