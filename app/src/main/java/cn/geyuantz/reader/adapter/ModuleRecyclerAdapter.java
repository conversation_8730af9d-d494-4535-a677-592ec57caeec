/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.ModuleVo;
import cn.geyuantz.reader.handler.ModuleHandler;
import com.xuexiang.xpage.base.XPageFragment;
import com.xuexiang.xui.widget.imageview.ImageLoader;
import com.xuexiang.xui.widget.imageview.RadiusImageView;

import java.util.List;

/**
 * 模块RecyclerView适配器
 */
public class ModuleRecyclerAdapter extends RecyclerView.Adapter<ModuleRecyclerAdapter.ModuleViewHolder> {
    
    private List<ModuleVo> mModuleList;
    private XPageFragment mFragment;
    
    public ModuleRecyclerAdapter(List<ModuleVo> moduleList, XPageFragment fragment) {
        this.mModuleList = moduleList;
        this.mFragment = fragment;
    }
    
    /**
     * 更新数据
     */
    public void updateData(List<ModuleVo> moduleList) {
        this.mModuleList = moduleList;
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public ModuleViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.adapter_common_grid_item, parent, false);
        return new ModuleViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ModuleViewHolder holder, int position) {
        ModuleVo moduleVo = mModuleList.get(position);
        holder.bind(moduleVo, mFragment);
    }
    
    @Override
    public int getItemCount() {
        return mModuleList != null ? mModuleList.size() : 0;
    }
    
    static class ModuleViewHolder extends RecyclerView.ViewHolder {
        private RadiusImageView radiusImageView;
        private TextView titleTextView;
        private TextView subTitleTextView;
        
        public ModuleViewHolder(@NonNull View itemView) {
            super(itemView);
            radiusImageView = itemView.findViewById(R.id.riv_item);
            titleTextView = itemView.findViewById(R.id.tv_title);
            subTitleTextView = itemView.findViewById(R.id.tv_sub_title);
        }
        
        public void bind(ModuleVo moduleVo, XPageFragment fragment) {
            // 设置模块数据
            subTitleTextView.setText(moduleVo.getModuleName());
            subTitleTextView.setTextColor(android.graphics.Color.rgb(139, 0, 0));
            
            // 设置图标
            if (moduleVo.getModuleImg() != null && !moduleVo.getModuleImg().isEmpty()) {
                // 如果有图标URL，加载图标
                ImageLoader.get().loadImage(radiusImageView, moduleVo.getModuleImg());
                radiusImageView.setVisibility(View.VISIBLE);
                titleTextView.setVisibility(View.GONE);
            } else {
                // 如果没有图标，显示文字
                radiusImageView.setVisibility(View.GONE);
                titleTextView.setVisibility(View.VISIBLE);
                String displayText = moduleVo.getModuleName().length() > 0 ? 
                    moduleVo.getModuleName().substring(0, 1) : "模";
                titleTextView.setText(displayText);
            }
            
            // 设置点击事件，直接使用ModuleHandler处理
            itemView.setOnClickListener(v -> {
                if (fragment != null) {
                    ModuleHandler moduleHandler = new ModuleHandler(fragment);
                    moduleHandler.handlerClick(moduleVo);
                }
            });
        }
    }
}
