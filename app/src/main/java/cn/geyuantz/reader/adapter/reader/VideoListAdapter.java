package cn.geyuantz.reader.adapter.reader;

import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.vo.GVideoVo;
import com.xuexiang.xui.adapter.recyclerview.BaseRecyclerAdapter;
import com.xuexiang.xui.adapter.recyclerview.RecyclerViewHolder;

public class VideoListAdapter extends BaseRecyclerAdapter<GVideoVo> {


    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.item_video_list_item;
    }

    @Override
    protected void bindData(@NonNull RecyclerViewHolder holder, int position, GVideoVo item) {
        if (item != null) {
            TextView title = holder.findViewById(R.id.tv_title);
            title.setText(item.getVideoName());
            ImageView ivCover = holder.findViewById(R.id.iv_cover);
            //加载网络图片
            Glide.with(ivCover.getContext())
                    .load(item.getVideoCover())
                    .into(ivCover);
        }
    }
}
