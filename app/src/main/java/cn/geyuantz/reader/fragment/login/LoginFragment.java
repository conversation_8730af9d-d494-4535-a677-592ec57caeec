/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.fragment.login;

import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.vo.LoginVo;
import cn.geyuantz.reader.utils.Utils;
import cn.geyuantz.reader.MyApp;
import cn.geyuantz.reader.R;
import cn.geyuantz.reader.activity.MainActivity;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.subscriber.TipRequestSubscriber;
import cn.geyuantz.reader.databinding.FragmentLoginBinding;
import cn.geyuantz.reader.utils.TokenUtils;
import com.xuexiang.xaop.annotation.SingleClick;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xhttp2.exception.ApiException;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xpage.enums.CoreAnim;
import com.xuexiang.xui.utils.ResUtils;
import com.xuexiang.xui.utils.ThemeUtils;
import com.xuexiang.xui.widget.actionbar.TitleBar;
import com.xuexiang.xutil.app.ActivityUtils;


/**
 * 登录页面
 *
 * <AUTHOR>
 * @since 2019-11-17 22:15
 */
@Page(anim = CoreAnim.none)
public class LoginFragment extends BaseFragment<FragmentLoginBinding> implements View.OnClickListener {

    private View mJumpView;


    @NonNull
    @Override
    protected FragmentLoginBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentLoginBinding.inflate(inflater, container, attachToRoot);
    }

    @Override
    protected TitleBar initTitle() {
        TitleBar titleBar = super.initTitle()
                .setImmersive(true);
        titleBar.setBackgroundColor(Color.TRANSPARENT);
        titleBar.setTitle("");
        titleBar.setLeftImageDrawable(ResUtils.getVectorDrawable(getContext(), R.drawable.ic_login_close));
        titleBar.setActionTextColor(ThemeUtils.resolveColor(getContext(), R.attr.colorAccent));
        mJumpView = titleBar.addAction(new TitleBar.TextAction(R.string.title_jump_login) {
            @Override
            public void performAction(View view) {
                //重新登陆
                reLogin();
            }
        });
        return titleBar;
    }

    private void reLogin() {
        //获取androidId

        //请求登录接口
        XHttpProxy.proxy(ApiService.IDeviceService.class)
                .login(Utils.getDeviceId(), MyApp.softwareCode)
                .subscribe(new TipRequestSubscriber<LoginVo>() {
                    @Override
                    protected void onSuccess(LoginVo stringStringMap) {
                        Log.e("登录成功", stringStringMap.toString());
                        //储存token
                        TokenUtils.setToken(stringStringMap.getToken());
                        //登录成功，跳转到主页面
                        ActivityUtils.startActivity(MainActivity.class);
                    }
                    @Override
                    public void onError(ApiException e) {
                        super.onError(e);
                        //登录失败，刷新错误信息
                        binding.registerTip.setText(e.getMessage());
                    }
                });
    }

    @Override
    protected void initViews() {
        String tip = getActivity().getIntent().getStringExtra("tip");
        if (tip != null) {
            binding.registerTip.setText(tip);
        }
    }

    @Override
    protected void initListeners() {
        binding.btnLogin.setOnClickListener(this);
    }


    @SingleClick
    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_login) {

            //请求申请接口
            XHttpProxy.proxy(ApiService.IDeviceService.class)
                    .apply(Utils.getDeviceId(), MyApp.softwareCode, Utils.getAndroidId(), binding.etRemark.getEditValue(), binding.etDeviceOrder.getEditValue())
                    .subscribe(new TipRequestSubscriber<String>() {
                        @Override
                        protected void onSuccess(String stringApiResult) {
                            Log.e("申请成功", "申请成功");
                            binding.registerTip.setText(stringApiResult);
                        }

                        @Override
                        public void onError(ApiException e) {
                            super.onError(e);
                            //申请失败，刷新错误信息
                            binding.registerTip.setText(e.getMessage());
                        }
                    });

        }

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }


}

