package cn.geyuantz.reader.fragment.video;

import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.shuyu.gsyvideoplayer.GSYVideoManager;
import com.shuyu.gsyvideoplayer.builder.GSYVideoOptionBuilder;
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer;
import cn.geyuantz.reader.adapter.reader.ModernVideoPlayListAdapter;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.subscriber.NoTipRequestSubscriber;
import cn.geyuantz.reader.core.http.vo.GVideoVo;
import cn.geyuantz.reader.databinding.FragmentVideoPlayListBinding;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xui.widget.actionbar.TitleBar;

import java.util.List;

@Page
public class VideoPlayListFragment extends BaseFragment<FragmentVideoPlayListBinding> {

    private GVideoVo mVideoVo;
    private ModernVideoPlayListAdapter mVideoPlayListAdapter;
    private StandardGSYVideoPlayer mVideoPlayer;
    private String TAG = "VideoPlayListFragment";

    @NonNull
    @Override
    protected FragmentVideoPlayListBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentVideoPlayListBinding.inflate(inflater, container, attachToRoot);
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mVideoVo = (GVideoVo) getArguments().getSerializable("videoVo");
        Log.i(TAG, "initViews: " + mVideoVo);
    }



    @Override
    protected void initViews() {
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerView.setAdapter( mVideoPlayListAdapter = new ModernVideoPlayListAdapter());
        binding.refreshLayout.autoRefresh();
        mVideoPlayer = binding.detailPlayer;
        GSYVideoOptionBuilder gsyVideoOption = new GSYVideoOptionBuilder();
        gsyVideoOption
                .setIsTouchWiget(true)
                .setRotateViewAuto(false)
                .setLockLand(false)
                .setAutoFullWithSize(true)
                .setShowFullAnimation(false)
                .setNeedLockFull(false)
                .setCacheWithPlay(false)
                .build(mVideoPlayer);
        mVideoPlayer.getBackButton().setVisibility(View.GONE);
        mVideoPlayer.getFullscreenButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mVideoPlayer.startWindowFullscreen(getContext(), false, true);
            }
        });
    }


    @Override
    protected void initListeners() {
        binding.refreshLayout.setOnRefreshListener(refreshLayout -> {
            fetchVideoPlayList(true, refreshLayout);
        });

        mVideoPlayListAdapter.setOnItemClickListener((itemView, item, position) -> {
            //播放视频
            play(item);
            //设置当前播放位置
            mVideoPlayListAdapter.setCurrentPlayingPosition(position);
        });

    }

    //获取音频列表
    private void fetchVideoPlayList(boolean isRefresh, RefreshLayout refreshLayout) {
        XHttpProxy.proxy(ApiService.IVideoService.class)
                .getVideoPlayList(mVideoVo.getVideoId())
                .subscribe(
                        new NoTipRequestSubscriber<List<GVideoVo>>() {
                            @Override
                            protected void onSuccess(List<GVideoVo> gAudioVos) {
                                if (isRefresh) {
                                    mVideoPlayListAdapter.refresh(gAudioVos);
                                    refreshLayout.finishRefresh();
                                    //播放第一个
                                    if (!gAudioVos.isEmpty()) {
                                        play(gAudioVos.get(0));
                                        mVideoPlayListAdapter.setCurrentPlayingPosition(0);
                                    }
                                } else {
                                    mVideoPlayListAdapter.loadMore(gAudioVos);
                                    refreshLayout.finishLoadMore();
                                }
                            }
                        }
                );

    }

    //播放音频
    private void play(GVideoVo gVideoVo) {
        mVideoPlayer.setUp(gVideoVo.getVideoUrl(), false, gVideoVo.getVideoName());
        mVideoPlayer.startPlayLogic();
    }

    @Override
    protected TitleBar initTitle() {
        return super.initTitle().setTitle(mVideoVo.getVideoName());
    }



    @Override
    public void onDestroyView() {
        super.onDestroyView();
        //释放播放器
        mVideoPlayer.release();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        //全屏播放时，返回键退出全屏
        if (mVideoPlayer.isIfCurrentIsFullscreen()) {
            GSYVideoManager.backFromWindowFull(getContext());
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
}