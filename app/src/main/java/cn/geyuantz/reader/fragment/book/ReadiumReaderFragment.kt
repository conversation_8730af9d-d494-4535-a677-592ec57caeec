package cn.geyuantz.reader.fragment.book

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment

import androidx.lifecycle.lifecycleScope
import cn.geyuantz.reader.R
import cn.geyuantz.reader.databinding.FragmentReadiumReaderBinding
import kotlinx.coroutines.launch
import org.readium.r2.navigator.epub.EpubNavigatorFactory
import org.readium.r2.navigator.epub.EpubNavigatorFragment
import org.readium.r2.shared.publication.Publication
import org.readium.r2.shared.util.Try
import org.readium.r2.streamer.Streamer
import org.readium.r2.shared.publication.asset.FileAsset
import java.io.File

/**
 * Readium阅读器Fragment
 * 基于Readium-toolkit 2.4.1实现，参考官方示例
 */
class ReadiumReaderFragment : Fragment() {
    
    private var _binding: FragmentReadiumReaderBinding? = null
    private val binding get() = _binding!!

    private var streamer: Streamer? = null
    private var publication: Publication? = null
    lateinit var navigator: EpubNavigatorFragment

    // 图书文件路径
    private var bookPath: String? = null
    
    companion object {
        private const val ARG_BOOK_PATH = "book_path"
        private const val NAVIGATOR_FRAGMENT_TAG = "epub_navigator"
        
        fun newInstance(bookPath: String): ReadiumReaderFragment {
            return ReadiumReaderFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_BOOK_PATH, bookPath)
                }
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        bookPath = arguments?.getString(ARG_BOOK_PATH)

        // 初始化Streamer
        streamer = Streamer(requireContext())

        // 加载图书并设置Fragment工厂
        loadBookAndSetupFactory()
    }

    private fun loadBookAndSetupFactory() {
        val bookPath = this.bookPath ?: return
        val bookFile = File(bookPath)

        if (!bookFile.exists()) {
            showError("图书文件不存在: $bookPath")
            return
        }

        lifecycleScope.launch {
            loadBookAsync(bookFile)
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentReadiumReaderBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
        loadBook()
    }
    
    private fun setupUI() {
        // 设置返回按钮
        binding.ivBack.setOnClickListener {
            requireActivity().onBackPressed()
        }
        
        // 设置标题
        binding.tvTitle.text = "正在加载..."
    }
    
    private fun loadBook() {
        val path = bookPath
        if (path == null) {
            showError("图书路径为空")
            return
        }
        
        val bookFile = File(path)
        if (!bookFile.exists()) {
            showError("图书文件不存在")
            return
        }
        
        // 显示加载状态
        binding.progressBar.visibility = View.VISIBLE
        binding.readerContainer.visibility = View.GONE
        
        // 异步加载图书
        lifecycleScope.launch {
            try {
                loadBookAsync(bookFile)
            } catch (e: Exception) {
                showError("加载图书失败: ${e.message}")
            }
        }
    }
    
    private suspend fun loadBookAsync(bookFile: File) {
        val streamer = this.streamer ?: return
        
        try {
            // 创建FileAsset
            val asset = FileAsset(bookFile)
            
            // 使用Streamer打开图书
            val result = streamer.open(asset, allowUserInteraction = false)
            
            when (result) {
                is Try.Success -> {
                    publication = result.value
                    setupNavigator(result.value)
                }
                is Try.Failure -> {
                    showError("打开图书失败")
                }
            }
        } catch (e: Exception) {
            showError("加载图书异常: ${e.message}")
        }
    }
    
    private fun setupNavigator(publication: Publication) {
        try {
            // 先显示图书信息，确认Publication加载成功
            showBookInfo(publication)

            // 尝试创建EpubNavigatorFactory
            val navigatorFactory = EpubNavigatorFactory(publication)

            // 创建Fragment工厂
            val fragmentFactory = navigatorFactory.createFragmentFactory(
                initialLocator = null,
                configuration = EpubNavigatorFragment.Configuration()
            )

            // 设置Fragment工厂
            childFragmentManager.fragmentFactory = fragmentFactory

            // 延迟添加Fragment
            binding.root.post {
                addNavigatorFragment()
            }

        } catch (e: Exception) {
            showError("初始化阅读器失败: ${e.message}\n${e.stackTrace.joinToString("\n")}")
        }
    }

    private fun addNavigatorFragment() {
        try {
            val navigatorFragmentTag = "epub_navigator"

            childFragmentManager.beginTransaction()
                .replace(R.id.reader_container, EpubNavigatorFragment::class.java, Bundle(), navigatorFragmentTag)
                .commitNow()

            val fragment = childFragmentManager.findFragmentByTag(navigatorFragmentTag)
            if (fragment is EpubNavigatorFragment) {
                navigator = fragment

                // 隐藏加载状态，显示阅读器
                binding.progressBar.visibility = View.GONE
                binding.readerContainer.visibility = View.VISIBLE

            } else {
                showError("Fragment创建失败，类型: ${fragment?.javaClass?.simpleName}")
            }

        } catch (e: Exception) {
            showError("添加Navigator Fragment失败: ${e.message}")
        }
    }

    private fun showBookInfo(publication: Publication) {
        val metadata = publication.metadata
        // 设置标题
        binding.tvTitle.text = metadata.title ?: "未知标题"
    }
    
    private fun showError(message: String) {
        binding.progressBar.visibility = View.GONE
        binding.readerContainer.visibility = View.GONE
        
        Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()
        
        // 可以显示错误页面
        binding.tvTitle.text = "加载失败"
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    
    override fun onDestroy() {
        super.onDestroy()

        // 清理资源
        publication?.close()
        publication = null
        streamer = null
    }

    // 暂时移除Listener实现，先让基础功能工作

}
