package cn.geyuantz.reader.fragment.audio;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.widget.ModernAudioPlayer;
import cn.geyuantz.reader.adapter.reader.ModernAudioListAdapter;
import cn.geyuantz.reader.manager.AudioPlayerManager;
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.bo.PageQuery;
import cn.geyuantz.reader.core.http.subscriber.NoTipRequestSubscriber;
import cn.geyuantz.reader.core.http.vo.GAudioVo;
import cn.geyuantz.reader.core.http.vo.GClassVo;
import cn.geyuantz.reader.core.http.bo.RAudioList;
import cn.geyuantz.reader.databinding.FragmentAudioListBinding;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xui.widget.actionbar.TitleBar;

import java.util.List;

@Page
public class AudioListFragment extends BaseFragment<FragmentAudioListBinding> {
    private GClassVo mClassVo;
    String TAG = "AudioListFragment";
    private ModernAudioListAdapter audioListAdapter;
    private int mPage = -1;
    private ModernAudioPlayer modernAudioPlayer;
    private AudioPlayerManager audioPlayerManager;
    private StandardGSYVideoPlayer hiddenAudioPlayer;

    @NonNull
    @Override
    protected FragmentAudioListBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentAudioListBinding.inflate(inflater, container, attachToRoot);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mClassVo = (GClassVo) getArguments().getSerializable("classVo");
        Log.i(TAG, "initViews: " + mClassVo);
    }

    @Override
    protected void initViews() {
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerView.setAdapter(audioListAdapter = new ModernAudioListAdapter());
        // 设置RecyclerView引用到适配器，用于滚动功能
        audioListAdapter.setRecyclerView(binding.recyclerView);
        binding.refreshLayout.autoRefresh();
        // 初始化隐藏的GSYVideoPlayer
        hiddenAudioPlayer = binding.hiddenAudioPlayer;

        // 初始化音频播放管理器
        audioPlayerManager = AudioPlayerManager.getInstance(getContext());
        audioPlayerManager.initWithGSYVideoPlayer(hiddenAudioPlayer);

        // 初始化现代化音频播放器
        modernAudioPlayer = binding.modernAudioPlayer;
        modernAudioPlayer.setOnAudioControlListener(new ModernAudioPlayer.OnAudioControlListener() {
            @Override
            public void onPlayPause(boolean isPlaying) {
                audioPlayerManager.togglePlayPause();
            }

            @Override
            public void onPrevious() {
                audioPlayerManager.playPrevious();
            }

            @Override
            public void onNext() {
                audioPlayerManager.playNext();
            }

            @Override
            public void onSeekTo(int position) {
                audioPlayerManager.seekTo(position);
            }
        });

        // 设置音频播放器监听器
        audioPlayerManager.setOnAudioPlayerListener(new AudioPlayerManager.OnAudioPlayerListener() {
            @Override
            public void onPlayStateChanged(boolean isPlaying) {
                modernAudioPlayer.setPlayState(isPlaying);
                // 更新列表中的播放状态，控制GIF动画
                audioListAdapter.setPlayingState(isPlaying);
            }

            @Override
            public void onProgressUpdate(int currentPosition, int duration) {
                modernAudioPlayer.updateProgress(currentPosition, duration);
            }

            @Override
            public void onAudioChanged(GAudioVo audio, int index) {
                modernAudioPlayer.setAudioInfo(audio);
                // 更新列表中的播放状态和滚动位置
                audioListAdapter.setCurrentPlayingPosition(index);
            }

            @Override
            public void onError(String error) {
                // 显示错误信息
                Log.e(TAG, "Audio player error: " + error);
                // 显示Toast提示给用户
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        android.widget.Toast.makeText(getContext(), error, android.widget.Toast.LENGTH_SHORT).show();
                    });
                }
            }

            @Override
            public void onNeedLoadMore(AudioPlayerManager.OnLoadMoreCallback callback) {
                // 触发加载更多，并在完成后回调
                loadMoreWithCallback(callback);
            }
        });
    }

    /**
     * 手动触发加载更多
     */
    private void loadMore() {
        if (binding != null && binding.refreshLayout != null) {
            mPage++;
            fetchAudioList(mPage, false, null);
        }
    }

    /**
     * 带回调的加载更多
     */
    private void loadMoreWithCallback(AudioPlayerManager.OnLoadMoreCallback callback) {
        if (binding != null && binding.refreshLayout != null) {
            mPage++;
            fetchAudioList(mPage, false, binding.refreshLayout, callback);
        } else if (callback != null) {
            callback.onLoadMoreComplete(false);
        }
    }

    @Override
    protected void initListeners() {
        binding.refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                mPage++;
                fetchAudioList(mPage, false, refreshLayout);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                mPage = 1;
                fetchAudioList(mPage, true, refreshLayout);
            }
        });

        audioListAdapter.setOnItemClickListener((itemView, item, position) -> {
            //播放音频
            playAudio(item);
            //设置当前播放位置
            audioListAdapter.setCurrentPlayingPosition(position);
        });

    }

    //获取音频列表
    private void fetchAudioList(int page, boolean isRefresh, RefreshLayout refreshLayout) {
        fetchAudioList(page, isRefresh, refreshLayout, null);
    }

    private void fetchAudioList(int page, boolean isRefresh, RefreshLayout refreshLayout, AudioPlayerManager.OnLoadMoreCallback callback) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(page);
        RAudioList rAudioList = new RAudioList().setPageQuery(pageQuery).setClassId(mClassVo.getClassId());
        XHttpProxy.proxy(ApiService.IAudioService.class)
                .getAudioList(rAudioList)
                .subscribe(
                        new NoTipRequestSubscriber<List<GAudioVo>>() {
                            @Override
                            protected void onSuccess(List<GAudioVo> gAudioVos) {
                                if (isRefresh) {
                                    audioListAdapter.refresh(gAudioVos);
                                    if (refreshLayout != null) {
                                        refreshLayout.finishRefresh();
                                    }
                                    // 设置音频列表到播放管理器
                                    audioPlayerManager.setAudioList(gAudioVos);
                                    //播放第一个音频
                                    if (!gAudioVos.isEmpty()) {
                                        playAudio(gAudioVos.get(0));
                                        audioListAdapter.setCurrentPlayingPosition(0);
                                    }
                                } else {
                                    audioListAdapter.loadMore(gAudioVos);
                                    if (refreshLayout != null) {
                                        refreshLayout.finishLoadMore();
                                    }
                                    // 更新音频列表到播放管理器（加载更多时不重置索引）
                                    audioPlayerManager.setAudioList(audioListAdapter.getData(), false);

                                    // 如果有回调，通知加载更多完成
                                    if (callback != null) {
                                        boolean hasMoreData = gAudioVos != null && !gAudioVos.isEmpty();
                                        callback.onLoadMoreComplete(hasMoreData);
                                    }
                                }


                            }

                            @Override
                            public void onError(com.xuexiang.xhttp2.exception.ApiException e) {
                                super.onError(e);
                                // 网络请求失败时，通知回调
                                if (callback != null) {
                                    callback.onLoadMoreComplete(false);
                                }
                            }
                        }
                );

    }

    //播放音频
    private void playAudio(GAudioVo gAudioVo) {
        // 使用音频播放管理器播放音频
        audioPlayerManager.playAudio(gAudioVo);
    }

    @Override
    protected TitleBar initTitle() {
        return super.initTitle().setTitle(mClassVo.getClassName());
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 释放音频播放器资源
        if (audioPlayerManager != null) {
            audioPlayerManager.release();
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        // 现代化音频播放器不需要处理全屏逻辑
        return super.onKeyDown(keyCode, event);
    }
}