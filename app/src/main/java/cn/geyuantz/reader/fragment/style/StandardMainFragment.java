/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.fragment.style;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.GridView;

import cn.geyuantz.reader.adapter.reader.ModuleAdapter;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.vo.DeviceInfoVo;
import cn.geyuantz.reader.core.http.vo.BannerVo;
import cn.geyuantz.reader.R;
import cn.geyuantz.reader.databinding.FragmentStandardMainBinding;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xpage.enums.CoreAnim;
import com.xuexiang.xui.utils.ThemeUtils;
import com.xuexiang.xui.widget.actionbar.TitleBar;

import java.util.List;

/**
 * 标准主页面
 */
@Page(anim = CoreAnim.present)
public class StandardMainFragment extends BaseFragment<FragmentStandardMainBinding> {

    private DeviceInfoVo mDeviceInfoVo;

    @Override
    protected TitleBar initTitle() {
        this.mDeviceInfoVo = (DeviceInfoVo) getArguments().getSerializable("deviceInfo");
        TitleBar titleBar = super.initTitle()
                .setImmersive(true);
        titleBar.setTitle(mDeviceInfoVo.getDeviceTitle());
        //取消返回按钮
        titleBar.disableLeftView();
        titleBar.setActionTextColor(ThemeUtils.resolveColor(getContext(), R.attr.colorAccent));
        return titleBar;
    }

    @Override
    protected void initViews() {
        List<BannerVo> bannerList = mDeviceInfoVo.getBannerList();
        //处理格源banner业务数据
        binding.banner.initGyBanner(bannerList,this);
        /*模块*/
        GridView gridModule = binding.gridModule;
        gridModule.setAdapter(new ModuleAdapter( this, mDeviceInfoVo.getModuleList(), Color.BLACK));

    }

    @NonNull
    @Override
    protected FragmentStandardMainBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentStandardMainBinding.inflate(inflater, container, attachToRoot);
    }
}