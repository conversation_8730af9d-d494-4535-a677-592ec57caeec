package cn.geyuantz.reader.fragment.book;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.content.Context;
import android.graphics.Color;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.scwang.smartrefresh.header.PhoenixHeader;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import cn.geyuantz.reader.MyApp;
import cn.geyuantz.reader.R;
import cn.geyuantz.reader.adapter.reader.BookAdapter;
import cn.geyuantz.reader.adapter.reader.CategoryBubbleAdapter;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.bo.PageQuery;
import cn.geyuantz.reader.core.http.bo.RBookList;
import cn.geyuantz.reader.core.http.subscriber.NoTipRequestSubscriber;
import cn.geyuantz.reader.core.http.vo.BookVo;
import cn.geyuantz.reader.core.http.vo.GClassVo;
import cn.geyuantz.reader.databinding.FragmentBookListBinding;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xui.adapter.recyclerview.GridDividerItemDecoration;
import com.xuexiang.xui.adapter.recyclerview.RecyclerViewHolder;
import com.xuexiang.xui.utils.DensityUtils;
import com.xuexiang.xui.utils.XToastUtils;
import com.xuexiang.xui.widget.actionbar.TitleBar;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Page
public class BookListFragment extends BaseFragment<FragmentBookListBinding> {

    RecyclerView mRecyclerView;
    SmartRefreshLayout mRefreshLayout;
    LinearLayout mLoadingLayout;
    private GridLayoutManager mGridLayoutManager;
    private int mPage = -1;
    private BookAdapter mBookAdapter;

    // 顶部工具栏相关
    private ImageView mIvBack;
    private EditText mEtSearch;
    private ImageView mIvSearch;
    private LinearLayout mLlCategories;
    private CategoryBubbleAdapter mCategoryAdapter;
    private List<GClassVo> mCategories = new ArrayList<>();
    private Long mCurrentClassId = null; // 当前选中的分类ID
    private String mCurrentSearchKeyword = ""; // 当前搜索关键词


    @Override
    protected TitleBar initTitle() {
        TitleBar titleBar = super.initTitle();
        titleBar.setVisibility(View.GONE);

        return titleBar;
    }

    @Override
    protected void initPage() {
        super.initPage();
    }

    @NonNull
    @Override
    protected FragmentBookListBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentBookListBinding.inflate(inflater, container, attachToRoot);
    }


    @Override
    protected void initViews() {
        // 初始化RecyclerView
        mRecyclerView = binding.recyclerView;
        mRefreshLayout = binding.refreshLayout;


        // 初始化加载提示
        mLoadingLayout = binding.llLoading;
        if (MyApp.isLand) {
            //横屏 - 使用4列布局
            mRecyclerView.setLayoutManager(mGridLayoutManager = new GridLayoutManager(getContext(), 4));
        }else {
            //竖屏 - 使用3列布局
            mRecyclerView.setLayoutManager(mGridLayoutManager = new GridLayoutManager(getContext(), 3));
        }
        mRecyclerView.setHasFixedSize(true);
        mRecyclerView.setAdapter(mBookAdapter = new BookAdapter());

        // 初始化顶部工具栏
        initToolbar();

        // 加载分类数据
        loadCategories();

        mRefreshLayout.autoRefresh();//第一次进入触发自动刷新，演示效果
    }

    /**
     * 初始化顶部工具栏
     */
    private void initToolbar() {
        // 获取工具栏中的控件
        mIvBack = binding.toolbar.ivBack;
        mEtSearch = binding.toolbar.etSearch;
        mIvSearch = binding.toolbar.ivSearch;
        mLlCategories = binding.toolbar.llCategories;

        // 确保控件不为空
        if (mIvBack == null || mEtSearch == null || mIvSearch == null || mLlCategories == null) {
            XToastUtils.error("工具栏控件初始化失败");
            return;
        }

        // 设置返回按钮点击事件
        mIvBack.setOnClickListener(v -> popToBack());

        // 设置搜索按钮点击事件
        mIvSearch.setOnClickListener(v -> performSearch());

        // 确保搜索框可以获取焦点和输入
        mEtSearch.setFocusable(true);
        mEtSearch.setFocusableInTouchMode(true);
        mEtSearch.setClickable(true);
        mEtSearch.setCursorVisible(true);
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        // 隐藏软键盘
        hideKeyboard();
        // 更新搜索关键词
        mCurrentSearchKeyword = mEtSearch.getText().toString().trim();
        // 重置页码并刷新
        mPage = -1;
        mRefreshLayout.autoRefresh();
    }

    /**
     * 隐藏软键盘
     */
    private void hideKeyboard() {
        if (getActivity() != null && mEtSearch != null) {
            InputMethodManager imm = (InputMethodManager) getActivity().getSystemService(getActivity().INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.hideSoftInputFromWindow(mEtSearch.getWindowToken(), 0);
            }
            mEtSearch.clearFocus();
        }
    }

    /**
     * 加载分类数据
     */
    private void loadCategories() {
        XHttpProxy.proxy(ApiService.IClassService.class)
                .getClassList(1L) // 图书分类ID为1
                .subscribe(new NoTipRequestSubscriber<List<GClassVo>>() {
                    @Override
                    protected void onSuccess(List<GClassVo> categories) {
                        mCategories.clear();

                        // 添加"全部"分类
                        GClassVo allCategory = new GClassVo();
                        allCategory.setClassId(null);
                        allCategory.setClassName("全部");
                        mCategories.add(allCategory);

                        if (categories != null) {
                            mCategories.addAll(categories);
                        }

                        setupCategoryBubbles();
                    }
                });
    }

    /**
     * 设置分类气泡
     */
    private void setupCategoryBubbles() {
        mLlCategories.removeAllViews();

        for (int i = 0; i < mCategories.size(); i++) {
            GClassVo category = mCategories.get(i);

            // 创建分类气泡视图
            View bubbleView = LayoutInflater.from(getContext())
                    .inflate(R.layout.item_category_bubble, mLlCategories, false);
            TextView tvCategory = bubbleView.findViewById(R.id.tv_category);
            tvCategory.setText(category.getClassName());

            // 设置选中状态
            boolean isSelected = (i == 0 && mCurrentClassId == null) ||
                               (category.getClassId() != null && category.getClassId().equals(mCurrentClassId));
            tvCategory.setSelected(isSelected);

            // 设置点击事件
            final int position = i;
            bubbleView.setOnClickListener(v -> {
                // 更新选中状态
                updateCategorySelection(position);
                // 更新当前分类ID
                mCurrentClassId = category.getClassId();
                // 刷新数据
                mPage = -1;
                mRefreshLayout.autoRefresh();
            });

            // 确保气泡可以点击
            bubbleView.setClickable(true);
            bubbleView.setFocusable(true);

            mLlCategories.addView(bubbleView);
        }
    }

    /**
     * 更新分类选中状态
     */
    private void updateCategorySelection(int selectedPosition) {
        for (int i = 0; i < mLlCategories.getChildCount(); i++) {
            View childView = mLlCategories.getChildAt(i);
            TextView tvCategory = childView.findViewById(R.id.tv_category);
            tvCategory.setSelected(i == selectedPosition);
        }
    }

    @Override
    protected void initListeners() {
        mRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(final @NonNull RefreshLayout refreshLayout) {
                mPage++;
                fetchBookList(mPage, false, refreshLayout);
            }

            @Override
            public void onRefresh(final @NonNull RefreshLayout refreshLayout) {
                mPage = 1;
                fetchBookList(mPage, true, refreshLayout);
            }
        });
        mBookAdapter.setOnItemClickListener(new RecyclerViewHolder.OnItemClickListener<BookVo>() {
            @Override
            public void onItemClick(View itemView, BookVo item, int position) {
                //跳转到图书详情页面
                openNewPage(BookInfoFragment.class, "bookVo", item);
            }
        });

    }

    //获取图书列表
    private void fetchBookList(int page, boolean isRefresh, RefreshLayout refreshLayout) {
        // 如果是第一次加载（刷新且页码为1），显示加载提示
        if (isRefresh && page == 1 && mBookAdapter.getItemCount() == 0) {
            showLoading(true);
        }

        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(page);
        RBookList rBookList = new RBookList().setPageQuery(pageQuery);

        // 设置分类ID
        if (mCurrentClassId != null) {
            rBookList.setClassId(mCurrentClassId);
        }

        // 设置搜索关键词
        if (!TextUtils.isEmpty(mCurrentSearchKeyword)) {
            rBookList.getGBookBo().setBookName(mCurrentSearchKeyword);
        }

        XHttpProxy.proxy(ApiService.IBookService.class)
                .getBookList(rBookList)
                .subscribe(new NoTipRequestSubscriber<List<BookVo>>() {
                    @Override
                    protected void onSuccess(List<BookVo> bookVos) {
                        // 隐藏加载提示
                        showLoading(false);

                        //判断是刷新还是加载更多
                        if (isRefresh) {
                            //刷新
                            mBookAdapter.refresh(bookVos);
                            refreshLayout.finishRefresh();
                        } else {
                            //加载更多
                            if (!bookVos.isEmpty()) {
                                mBookAdapter.loadMore(bookVos);
                                refreshLayout.finishLoadMore();
                            } else {
                                XToastUtils.toast("加载完成");
                                refreshLayout.finishLoadMoreWithNoMoreData();
                            }
                        }
                    }
                });
    }

    /**
     * 显示或隐藏加载提示
     */
    private void showLoading(boolean show) {
        if (mLoadingLayout != null) {
            mLoadingLayout.setVisibility(show ? View.VISIBLE : View.GONE);
        }
    }

}