package cn.geyuantz.reader.fragment.newsPaper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.kunminx.linkage.LinkageRecyclerView;
import com.kunminx.linkage.adapter.viewholder.LinkagePrimaryViewHolder;
import com.kunminx.linkage.adapter.viewholder.LinkageSecondaryViewHolder;
import com.kunminx.linkage.bean.BaseGroupedItem;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.subscriber.NoTipRequestSubscriber;
import cn.geyuantz.reader.core.http.vo.GNewspaperVo;
import cn.geyuantz.reader.core.http.vo.NewsPaperListVo;
import cn.geyuantz.reader.databinding.FragmentNewsPaperBinding;
import cn.geyuantz.reader.linkage.custom.CustomGroupedItem;
import cn.geyuantz.reader.linkage.custom.CustomLinkagePrimaryAdapterConfig;
import cn.geyuantz.reader.linkage.custom.CustomLinkageSecondaryAdapterConfig;
import cn.geyuantz.reader.utils.Utils;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xui.utils.WidgetUtils;
import com.xuexiang.xui.widget.dialog.LoadingDialog;

import java.util.ArrayList;
import java.util.List;

@Page(name = "报纸")
public class NewsPaperFragment extends BaseFragment<FragmentNewsPaperBinding> implements CustomLinkagePrimaryAdapterConfig.OnPrimaryItemClickListener, CustomLinkageSecondaryAdapterConfig.OnSecondaryItemClickListener {
    LinkageRecyclerView linkage;

    @NonNull
    @Override
    protected FragmentNewsPaperBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentNewsPaperBinding.inflate(inflater, container, attachToRoot);
    }

    @Override
    protected com.xuexiang.xui.widget.actionbar.TitleBar initTitle() {
        // 返回null来去掉标题栏
        return null;
    }

    @Override
    protected void initViews() {
        this.linkage=binding.linkage;

        // 设置返回按钮点击事件
        binding.ivBack.setOnClickListener(v -> {
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
        });
        //提示数据加载中 dialog
        LoadingDialog loadingDialog = WidgetUtils.getLoadingDialog(getContext())
                .setIconScale(0.4F)
                .setLoadingSpeed(8);

        loadingDialog.show();

        XHttpProxy.proxy(ApiService.IPaperService.class)
                .getPaperList()
                .subscribe(new NoTipRequestSubscriber<List<NewsPaperListVo>>() {
                    @Override
                    protected void onSuccess(List<NewsPaperListVo> newsPaperListVos) {
                        //解析数据
                        List<CustomGroupedItem> items = new ArrayList<>();
                        for (NewsPaperListVo newsPaperListVo : newsPaperListVos) {
                            CustomGroupedItem item = new CustomGroupedItem(true, newsPaperListVo.getClassName());
                            items.add(item);
                            for (GNewspaperVo gNewspaperVo : newsPaperListVo.getNewspaperList()) {
                                CustomGroupedItem.ItemInfo itemInfo = new CustomGroupedItem.ItemInfo(gNewspaperVo.getNewspaperTitle(), newsPaperListVo.getClassName(), gNewspaperVo.getNewspaperTitle(), null, gNewspaperVo.getNewspaperUrl());
                                items.add(new CustomGroupedItem(itemInfo));
                            }
                         }
                        linkage.init(items,new CustomLinkagePrimaryAdapterConfig(NewsPaperFragment.this),new CustomLinkageSecondaryAdapterConfig(NewsPaperFragment.this,getContext()));
                        // 禁用平滑滚动，点击后直接显示内容
                        linkage.setScrollSmoothly(false);
                        loadingDialog.dismiss();
                    }
                });
    }

    @Override
    public void onSecondaryItemClick(LinkageSecondaryViewHolder holder, ViewGroup view, BaseGroupedItem<CustomGroupedItem.ItemInfo> item) {
         Utils.goWeb(getContext(),item.info.getUrl());
    }

    @Override
    public void onPrimaryItemClick(LinkagePrimaryViewHolder holder, View view, String title) {

    }
}