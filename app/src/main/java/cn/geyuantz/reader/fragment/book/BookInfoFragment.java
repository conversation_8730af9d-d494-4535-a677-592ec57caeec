package cn.geyuantz.reader.fragment.book;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.animation.ObjectAnimator;
import android.animation.AnimatorSet;

import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.vo.BookVo;
import cn.geyuantz.reader.databinding.FragmentBookInfoBinding;
import lombok.extern.slf4j.Slf4j;

import com.bumptech.glide.Glide;
import com.xuexiang.xhttp2.XHttp;
import com.xuexiang.xhttp2.callback.DownloadProgressCallBack;
import com.xuexiang.xhttp2.exception.ApiException;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xqrcode.XQRCode;
import com.xuexiang.xui.utils.XToastUtils;
import com.xuexiang.xui.widget.actionbar.TitleBar;
import com.xuexiang.xui.widget.dialog.materialdialog.MaterialDialog;
import com.xuexiang.xutil.app.PathUtils;
import com.xuexiang.xutil.file.FileUtils;

import java.io.File;

/**
 * 图书信息
 */
@Slf4j
@Page
public class BookInfoFragment extends BaseFragment<FragmentBookInfoBinding> {
    private BookVo mBookVo;

    @Override
    protected TitleBar initTitle() {
        mBookVo = (BookVo) getArguments().getSerializable("bookVo");
        // 完全隐藏标题栏
        return null;
    }

    @NonNull
    @Override
    protected FragmentBookInfoBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentBookInfoBinding.inflate(inflater, container, attachToRoot);
    }

    private boolean isShowingQRCode = false;

    @Override
    protected void initViews() {
        // 设置图书信息
        setupBookInfo();

        // 设置按钮点击事件
        setupButtonListeners();

        // 设置返回键点击事件
        setupBackButton();

        // 生成二维码
        generateQRCode();
    }

    private void setupBookInfo() {
        // 设置书名
        binding.tvBookName.setText(mBookVo.getBookName());

        // 设置作者
        String author = mBookVo.getBookAuthor();
        if (author != null && !author.isEmpty()) {
            binding.tvBookAuthor.setText("作者：" + author);
        } else {
            binding.tvBookAuthor.setText("作者：未知");
        }

        // 设置简介（BookVo中没有简介字段，显示默认文本）
        binding.tvBookDescription.setText("点击下方按钮开始阅读这本精彩的图书");

        // 加载封面图片
        String coverUrl = mBookVo.getBookCover();
        if (coverUrl != null && !coverUrl.isEmpty()) {
            Glide.with(this)
                    .load(coverUrl)
                    .placeholder(R.mipmap.ic_launcher)
                    .error(R.mipmap.ic_launcher)
                    .into(binding.ivBookCover);
        }
    }

    private void setupButtonListeners() {
        // 本机阅读按钮
        binding.btnReadLocal.setOnClickListener(v -> {
            String url = mBookVo.getBookUrl();
            String savePath = PathUtils.getAppIntFilesPath();
            String saveName = url.substring(url.lastIndexOf("/") + 1);
            String saveFilePath = savePath + File.separator + saveName;

            if (FileUtils.isFileExists(saveFilePath)) {
                openBook(saveFilePath);
            } else {
                downloadBook(url, savePath, saveName);
            }
        });

        // 扫码阅读按钮
        binding.btnReadQrcode.setOnClickListener(v -> {
            toggleCoverAndQRCode();
        });
    }

    private void setupBackButton() {
        if (binding.ivBack != null) {
            // 设置点击事件
            binding.ivBack.setOnClickListener(v -> {
                // 直接调用Activity的onBackPressed
                if (getActivity() != null) {
                    getActivity().onBackPressed();
                } else {
                    android.util.Log.d("BookInfoFragment", "Activity为null");
                }
            });
        }
    }

    private void generateQRCode() {
        String QRCodeUrl = "http://api.geyuantz.cn/app/read?bookId=" + mBookVo.getBookId();
        binding.ivQrCode.setImageBitmap(XQRCode.createQRCodeWithLogo(QRCodeUrl, 300, 300, null));
    }

    private void toggleCoverAndQRCode() {
        // 禁用按钮防止重复点击
        binding.btnReadQrcode.setEnabled(false);

        if (!isShowingQRCode) {
            // 显示二维码，隐藏封面
            showQRCodeWithSimpleAnimation();
        } else {
            // 显示封面，隐藏二维码
            showCoverWithSimpleAnimation();
        }
    }

    private void showQRCodeWithSimpleAnimation() {
        // 使用简单的淡入淡出动画
        binding.cvBookCover.animate()
                .alpha(0f)
                .setDuration(150)
                .withEndAction(() -> {
                    if (binding == null) return;

                    binding.cvBookCover.setVisibility(View.GONE);
                    binding.cvQrCode.setVisibility(View.VISIBLE);
                    binding.cvQrCode.setAlpha(0f);

                    binding.cvQrCode.animate()
                            .alpha(1f)
                            .setDuration(150)
                            .withEndAction(() -> {
                                if (binding == null) return;
                                binding.btnReadQrcode.setText("返回封面");
                                binding.btnReadQrcode.setEnabled(true);
                                isShowingQRCode = true;
                            })
                            .start();
                })
                .start();
    }

    private void showCoverWithSimpleAnimation() {
        // 使用简单的淡入淡出动画
        binding.cvQrCode.animate()
                .alpha(0f)
                .setDuration(150)
                .withEndAction(() -> {
                    if (binding == null) return;

                    binding.cvQrCode.setVisibility(View.GONE);
                    binding.cvBookCover.setVisibility(View.VISIBLE);
                    binding.cvBookCover.setAlpha(0f);

                    binding.cvBookCover.animate()
                            .alpha(1f)
                            .setDuration(150)
                            .withEndAction(() -> {
                                if (binding == null) return;
                                binding.btnReadQrcode.setText("扫码阅读");
                                binding.btnReadQrcode.setEnabled(true);
                                isShowingQRCode = false;
                            })
                            .start();
                })
                .start();
    }

    private void downloadBook(String url, String savePath, String saveName) {
        MaterialDialog dialog = new MaterialDialog.Builder(getContext())
                .title("正在下载图书...")
                .content("请稍候，正在为您准备阅读内容")
                .progress(false, 100, true)
                .cancelable(false)
                .build();

        XHttp.downLoad(url)
                .savePath(savePath)
                .saveName(saveName)
                .execute(new DownloadProgressCallBack<String>() {

                    @Override
                    public void onStart() {
                        dialog.show();
                    }

                    @Override
                    public void onError(ApiException e) {
                        dialog.dismiss();
                        XToastUtils.error("下载失败：" + e.getMessage());
                    }

                    @Override
                    public void update(long downLoadSize, long totalSize, boolean done) {
                        dialog.setProgress((int) (downLoadSize * 100 / totalSize));
                        if (done) {
                            dialog.dismiss();
                        }
                    }

                    @Override
                    public void onComplete(String path) {
                        dialog.dismiss();
                        openBook(path);
                    }
                });
    }
    //根据文件后缀名确定打开方式
    private void openBook(String bookUrl) {
        if (bookUrl.endsWith(".epub")) {
            openEpubBook(bookUrl);
        } else {
            XToastUtils.error("不支持的文件格式！");
        }
    }

    //打开书籍
    private void openEpubBook(String bookUrl) {
        // 使用新的Readium阅读器
        ReadiumReaderFragment readerFragment = ReadiumReaderFragment.Companion.newInstance(bookUrl);

        // 替换当前Fragment
        if (getActivity() != null) {
            getActivity().getSupportFragmentManager()
                    .beginTransaction()
                    .replace(android.R.id.content, readerFragment)
                    .addToBackStack(null)
                    .commit();
        }
    }
}