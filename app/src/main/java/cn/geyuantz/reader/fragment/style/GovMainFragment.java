/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.fragment.style;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.viewpager2.widget.ViewPager2;

import com.bumptech.glide.Glide;
import cn.geyuantz.reader.MyApp;
import cn.geyuantz.reader.R;
import cn.geyuantz.reader.adapter.ModuleGridPagerAdapter;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.vo.BannerVo;
import cn.geyuantz.reader.core.http.vo.DeviceInfoVo;
import cn.geyuantz.reader.core.http.vo.ModuleVo;
import cn.geyuantz.reader.databinding.FragmentGovMainBinding;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xpage.enums.CoreAnim;
import com.xuexiang.xui.utils.ThemeUtils;
import com.xuexiang.xui.widget.actionbar.TitleBar;

import java.util.ArrayList;
import java.util.List;

/**
 * 政务主页面
 */
@Page(anim = CoreAnim.none)
public class GovMainFragment extends BaseFragment<FragmentGovMainBinding> {

    private DeviceInfoVo mDeviceInfoVo;
    private Animation mBlinkAnimation;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置党建风格标志
        BaseFragment.setPartyBuildingStyle(true);
        initBlinkAnimation();
    }

    /**
     * 初始化闪烁动画 - 柔和的小箭头闪烁
     */
    private void initBlinkAnimation() {
        mBlinkAnimation = new AlphaAnimation(0.5f, 1f);
        mBlinkAnimation.setDuration(1000); // 动画持续时间1000ms，适中的闪烁
        mBlinkAnimation.setRepeatCount(Animation.INFINITE); // 无限重复
        mBlinkAnimation.setRepeatMode(Animation.REVERSE); // 反向重复
    }

    @Override
    protected TitleBar initTitle() {
        this.mDeviceInfoVo = (DeviceInfoVo) getArguments().getSerializable("deviceInfo");
        TitleBar titleBar = super.initTitle()
                .setImmersive(false);
        //机构标题
//        titleBar.setTitle(mDeviceInfoVo.getDeviceTitle());
        titleBar.setTitle("智慧党建平台");
        //样式设置
        titleBar.setCenterTextBold(true);
        //设置背景透明，背景将在文本上设置
        titleBar.setBackgroundColor(Color.TRANSPARENT);
        //文字颜色为深红色，类似图片中的效果
        titleBar.setTitleColor(Color.rgb(220, 20, 60));
        //字体大小适中
        titleBar.setTitleSize(42);
        //设置上方外边距，使标题栏下移
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) titleBar.getLayoutParams();
        if (params != null) {
            params.topMargin = 80;  // 减少上边距，让下方元素更贴近标题
            // 移除左右边距，让标题栏宽度自适应
            params.leftMargin = 0;
            params.rightMargin = 0;
            titleBar.setLayoutParams(params);
        }

        //保持适当高度
        titleBar.setHeight(100);
        //添加阴影效果
        titleBar.setElevation(12);

        //获取中间的文本视图并自定义样式
        TextView centerText = titleBar.getCenterText();
        if (centerText != null) {
            // 设置文本背景为粉色纯色
            centerText.setBackgroundResource(R.drawable.bg_title_pink_solid);

            // 给文字添加阴影
            centerText.setShadowLayer(2, 1, 1, Color.rgb(255, 255, 255));

            // 设置内边距让文字在圆角背景中有足够空间
            centerText.setPadding(40, 15, 40, 15);

            // 设置文字样式
            centerText.setTypeface(centerText.getTypeface(), android.graphics.Typeface.BOLD);
        }
        //取消返回按钮
        titleBar.disableLeftView();
        titleBar.setActionTextColor(ThemeUtils.resolveColor(requireContext(), R.attr.colorAccent));
        return titleBar;
    }

    @Override
    protected void initViews() {
        //设置背景图片 根据横竖屏设置不同的背景
        getRootView().setBackgroundResource(MyApp.isLand ? R.drawable.bg_gov_land : R.drawable.bg_gov);
        List<BannerVo> rawBannerList = mDeviceInfoVo.getBannerList();
        List<BannerVo> bannerList = new ArrayList<>();
        List<BannerVo> bannerList1 = new ArrayList<>();
        //最后一个放bannerList1,其他放bannerList
        for (int i = 0; i < rawBannerList.size(); i++) {
            if (i == rawBannerList.size() - 1) {
                bannerList1.add(rawBannerList.get(i));
            } else {
                bannerList.add(rawBannerList.get(i));
            }
        }
        //处理格源banner业务数据
        binding.banner.initGyBanner(bannerList, this);
        if (binding.banner1 != null) {
           Glide.with(this).load(bannerList1.get(0).getBannerUrl()).into(binding.banner1);
        }
        /*模块 - 两排水平滑动*/
        setupModuleLayout();

    }

    /**
     * 设置模块翻页布局
     */
    private void setupModuleLayout() {
        List<ModuleVo> moduleList = mDeviceInfoVo.getModuleList();
        if (moduleList == null || moduleList.isEmpty()) {
            return;
        }

        ViewPager2 viewPager = binding.vpModules;
        FrameLayout arrowLeftContainer = binding.flArrowLeft;
        FrameLayout arrowRightContainer = binding.flArrowRight;
        ImageView arrowLeft = binding.ivArrowLeft;
        ImageView arrowRight = binding.ivArrowRight;

        // 设置GridView适配器，自动分页
        ModuleGridPagerAdapter adapter = new ModuleGridPagerAdapter(moduleList, this);
        viewPager.setAdapter(adapter);

        // 设置箭头点击事件
        setupArrowClickListeners(viewPager, arrowLeftContainer, arrowRightContainer);

        // 如果有多页，设置页面变化监听
        int pageCount = adapter.getItemCount();
        if (pageCount > 1) {
            setupPageChangeListener(viewPager, arrowLeft, arrowRight, arrowLeftContainer, arrowRightContainer, pageCount);
            // 初始状态：第一页，只显示右箭头
            updateArrowVisibility(0, pageCount, arrowLeft, arrowRight, arrowLeftContainer, arrowRightContainer);
        } else {
            // 只有一页，隐藏所有箭头
            hideArrowWithAnimation(arrowLeft, arrowLeftContainer);
            hideArrowWithAnimation(arrowRight, arrowRightContainer);
        }
    }

    /**
     * 设置箭头点击事件
     */
    private void setupArrowClickListeners(ViewPager2 viewPager, FrameLayout arrowLeftContainer, FrameLayout arrowRightContainer) {
        // 左箭头点击事件
        arrowLeftContainer.setOnClickListener(v -> {
            int currentItem = viewPager.getCurrentItem();
            if (currentItem > 0) {
                viewPager.setCurrentItem(currentItem - 1, true);
            }
        });

        // 右箭头点击事件
        arrowRightContainer.setOnClickListener(v -> {
            int currentItem = viewPager.getCurrentItem();
            if (currentItem < viewPager.getAdapter().getItemCount() - 1) {
                viewPager.setCurrentItem(currentItem + 1, true);
            }
        });
    }

    /**
     * 设置页面变化监听，控制箭头显示
     */
    private void setupPageChangeListener(ViewPager2 viewPager, ImageView arrowLeft, ImageView arrowRight,
                                       FrameLayout arrowLeftContainer, FrameLayout arrowRightContainer, int totalPages) {
        viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                updateArrowVisibility(position, totalPages, arrowLeft, arrowRight, arrowLeftContainer, arrowRightContainer);
            }
        });
    }

    /**
     * 更新箭头显示状态
     */
    private void updateArrowVisibility(int currentPage, int totalPages, ImageView arrowLeft, ImageView arrowRight,
                                     FrameLayout arrowLeftContainer, FrameLayout arrowRightContainer) {
        // 左箭头：不是第一页时显示
        if (currentPage > 0) {
            showArrowWithAnimation(arrowLeft, arrowLeftContainer);
        } else {
            hideArrowWithAnimation(arrowLeft, arrowLeftContainer);
        }

        // 右箭头：不是最后一页时显示
        if (currentPage < totalPages - 1) {
            showArrowWithAnimation(arrowRight, arrowRightContainer);
        } else {
            hideArrowWithAnimation(arrowRight, arrowRightContainer);
        }
    }

    /**
     * 显示箭头并开始闪烁动画
     */
    private void showArrowWithAnimation(ImageView arrow, FrameLayout container) {
        if (container.getVisibility() != View.VISIBLE) {
            container.setVisibility(View.VISIBLE);
            arrow.startAnimation(mBlinkAnimation);
        }
    }

    /**
     * 隐藏箭头并停止动画
     */
    private void hideArrowWithAnimation(ImageView arrow, FrameLayout container) {
        if (container.getVisibility() == View.VISIBLE) {
            arrow.clearAnimation();
            container.setVisibility(View.GONE);
        }
    }




    @NonNull
    @Override
    protected FragmentGovMainBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentGovMainBinding.inflate(inflater, container, attachToRoot);
    }
}