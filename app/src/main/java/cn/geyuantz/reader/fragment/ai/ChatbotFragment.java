package cn.geyuantz.reader.fragment.ai;


import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import cn.geyuantz.reader.R;
import cn.geyuantz.reader.adapter.entity.IMsg;
import cn.geyuantz.reader.adapter.entity.User;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.bo.AIBo;
import cn.geyuantz.reader.core.http.vo.ModuleVo;
import cn.geyuantz.reader.core.http.vo.RobotDetailVo;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.subscriber.TipRequestSubscriber;
import cn.geyuantz.reader.databinding.FragmentChatbotBinding;
import cn.geyuantz.reader.utils.TokenUtils;
import com.xuexiang.xhttp2.XHttp;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xui.widget.actionbar.TitleBar;
import com.xuexiang.xutil.net.JSONUtils;
import com.xuexiang.xutil.tip.ToastUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Timer;
import java.util.concurrent.TimeUnit;

import android.widget.EditText;
import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.TextView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import cn.jiguang.imui.commons.ImageLoader;
import cn.jiguang.imui.commons.models.IMessage;
import cn.jiguang.imui.messages.MessageList;
import cn.jiguang.imui.messages.MsgListAdapter;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.internal.sse.RealEventSource;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;

@Page
public class ChatbotFragment extends BaseFragment<FragmentChatbotBinding> {

    private static final Logger log = LoggerFactory.getLogger(ChatbotFragment.class);
    MessageList messageList;
    MsgListAdapter<IMsg> msgAdapter;
    EditText messageInput;
    FloatingActionButton sendButton;
    FloatingActionButton helpButton;
    LinearLayout floatingQuestions;
    TextView question1, question2, question3;
    ModuleVo moduleVo;
    RobotDetailVo robotDetail;
    TitleBar titleBar;

    // 标志变量：是否已经显示过悬浮问题
    private boolean hasShownFloatingQuestions = false;
    // 标志变量：是否是第一次AI回答
    private boolean isFirstAIResponse = true;
    // 标志变量：是否已经获取机器人信息
    private boolean hasLoadedRobotInfo = false;

    @NonNull
    @Override
    protected FragmentChatbotBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentChatbotBinding.inflate(inflater, container, attachToRoot);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        moduleVo = (ModuleVo) getArguments().getSerializable("moduleVo");
    }

    @Override
    protected TitleBar initTitle() {
        // 初始显示模块名称，获取到机器人信息后会更新为机器人名称
//        titleBar = super.initTitle().setTitle(moduleVo.getModuleName());
        return null;
    }

    @Override
    protected void initViews() {
        //设置标题
        messageList = binding.msgList;
        messageInput = binding.etMessageInput;
        sendButton = binding.fabSend;
        helpButton = binding.fabHelp;
        floatingQuestions = binding.llFloatingQuestions;
        question1 = binding.tvQuestion1;
        question2 = binding.tvQuestion2;
        question3 = binding.tvQuestion3;

        // 设置现代化的输入界面
        setupModernChatInput();

        // 设置悬浮问题
        setupFloatingQuestions();

        msgAdapter = new MsgListAdapter<>("1", new MsgListAdapter.HoldersConfig(), new ImageLoader() {
            @Override
            public void loadAvatarImage(ImageView avatarImageView, String string) {
                // 检查Fragment是否还存在且附加到Activity
                if (!isAdded() || getActivity() == null || getActivity().isDestroyed()) {
                    return;
                }

                if (string != null) {
                    if (string.contains("robot")) {
                        // AI机器人头像 - 优先使用接口返回的头像，否则使用默认robot.png
                        String robotAvatarUrl = (robotDetail != null && robotDetail.getRobotAvatar() != null)
                                ? robotDetail.getRobotAvatar() : null;

                        try {
                            if (robotAvatarUrl != null && !robotAvatarUrl.trim().isEmpty()) {
                                // 使用接口返回的机器人头像
                                Glide.with(ChatbotFragment.this)
                                        .load(robotAvatarUrl)
                                        .circleCrop()
                                        .placeholder(R.drawable.robot)
                                        .error(R.drawable.robot)
                                        .into(avatarImageView);
                            } else {
                                // 使用默认机器人头像
                                Glide.with(ChatbotFragment.this)
                                        .load(R.drawable.robot)
                                        .circleCrop()
                                        .into(avatarImageView);
                            }
                        } catch (IllegalArgumentException e) {
                            // Fragment已销毁，设置默认头像
                            avatarImageView.setImageResource(R.drawable.robot);
                        }
                    } else if (string.contains("user")) {
                        // 用户头像 - 使用user.png
                        try {
                            Glide.with(ChatbotFragment.this)
                                    .load(R.drawable.user)
                                    .circleCrop()
                                    .into(avatarImageView);
                        } catch (IllegalArgumentException e) {
                            // Fragment已销毁，设置默认头像
                            avatarImageView.setImageResource(R.drawable.user);
                        }
                    } else {
                        // 其他头像图片，设置圆形裁剪
                        try {
                            Glide.with(ChatbotFragment.this)
                                    .load(string)
                                    .circleCrop()
                                    .placeholder(R.drawable.ic_default_head)
                                    .error(R.drawable.ic_default_head)
                                    .into(avatarImageView);
                        } catch (IllegalArgumentException e) {
                            // Fragment已销毁，设置默认头像
                            avatarImageView.setImageResource(R.drawable.ic_default_head);
                        }
                    }
                } else {
                    // 默认头像
                    try {
                        Glide.with(ChatbotFragment.this)
                                .load(R.drawable.ic_default_head)
                                .circleCrop()
                                .into(avatarImageView);
                    } catch (IllegalArgumentException e) {
                        // Fragment已销毁，设置默认头像
                        avatarImageView.setImageResource(R.drawable.ic_default_head);
                    }
                }
            }

            @Override
            public void loadImage(ImageView imageView, String string) {

            }

            @Override
            public void loadVideo(ImageView imageCover, String uri) {

            }
        });
        messageList.setAdapter(msgAdapter);

        // 先获取机器人信息，然后发送开场白
        loadRobotInfo();
    }

    /**
     * 设置现代化的聊天输入界面
     */
    private void setupModernChatInput() {
        // 发送按钮点击事件
        sendButton.setOnClickListener(v -> {
            String message = messageInput.getText().toString().trim();
            if (message.isEmpty()) {
                ToastUtils.toast("请输入内容");
                return;
            }

            // 添加点击动画效果
            v.animate()
                .scaleX(0.9f)
                .scaleY(0.9f)
                .setDuration(100)
                .withEndAction(() -> {
                    v.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start();
                })
                .start();

            // 发送消息
            sendUserMessage(message);
            // 清空输入框
            messageInput.setText("");
            // 请求AI回答
            responseUserMessage();
        });

        // 输入框回车发送
        messageInput.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEND ||
                    (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER && event.getAction() == KeyEvent.ACTION_DOWN)) {
                    sendButton.performClick();
                    return true;
                }
                return false;
            }
        });

        // 输入框文字变化监听，控制发送按钮状态
        messageInput.addTextChangedListener(new android.text.TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 根据输入内容改变发送按钮的状态和颜色
                boolean hasText = !s.toString().trim().isEmpty();
                sendButton.setEnabled(hasText);

                if (hasText) {
                    sendButton.setBackgroundTintList(android.content.res.ColorStateList.valueOf(0xFF4285F4));
                    sendButton.setAlpha(1.0f);
                } else {
                    sendButton.setBackgroundTintList(android.content.res.ColorStateList.valueOf(0xFFCCCCCC));
                    sendButton.setAlpha(0.6f);
                }
            }

            @Override
            public void afterTextChanged(android.text.Editable s) {}
        });

        // 初始状态设置发送按钮为禁用状态
        sendButton.setEnabled(false);
        sendButton.setBackgroundTintList(android.content.res.ColorStateList.valueOf(0xFFCCCCCC));
        sendButton.setAlpha(0.6f);
    }

    /**
     * 设置悬浮问题功能
     */
    private void setupFloatingQuestions() {
        // 问题内容和点击事件将在showFloatingQuestions()中设置

        // 问号按钮点击事件
        helpButton.setOnClickListener(v -> {
            if (robotDetail != null && robotDetail.getRobotQuestions() != null && !robotDetail.getRobotQuestions().isEmpty()) {
                if (floatingQuestions.getVisibility() == android.view.View.VISIBLE) {
                    hideFloatingQuestions();
                } else {
                    showFloatingQuestionsManually();
                }
            } else {
                ToastUtils.toast("暂无可用问题");
            }
        });

        // 输入框获得焦点时隐藏悬浮问题
        messageInput.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                hideFloatingQuestions();
            }
        });

        // 输入框点击时也隐藏悬浮问题
        messageInput.setOnClickListener(v -> hideFloatingQuestions());
    }

    /**
     * 手动显示悬浮问题（通过问号按钮触发，可以重复显示）
     */
    private void showFloatingQuestionsManually() {
        // 检查Fragment是否还存在且附加到Context
        if (!isAdded() || getActivity() == null) {
            Log.d("FloatingAnimation", "Fragment未附加到Context，取消显示悬浮问题");
            return;
        }

        // 检查是否有问题数据
        if (robotDetail == null || robotDetail.getRobotQuestions() == null || robotDetail.getRobotQuestions().isEmpty()) {
            Log.d("FloatingAnimation", "没有问题数据，取消显示悬浮问题");
            return;
        }

        // 手动显示悬浮问题（可以重复显示）
        if (floatingQuestions != null && floatingQuestions.getVisibility() == android.view.View.GONE) {

            // 设置问题内容和点击事件
            List<String> questions = robotDetail.getRobotQuestions();
            TextView[] questionViews = {question1, question2, question3};

            // 使用循环设置问题
            for (int i = 0; i < Math.min(questions.size(), questionViews.length); i++) {
                TextView questionView = questionViews[i];
                if (questionView != null) {
                    String questionText = questions.get(i);
                    questionView.setText(questionText);
                    questionView.setOnClickListener(v -> {
                        sendQuestionAndHideFloating(questionText);
                    });
                }
            }

            floatingQuestions.setVisibility(android.view.View.VISIBLE);

            // 安全地获取屏幕宽度，从屏幕右侧外开始
            int screenWidth;
            try {
                screenWidth = getResources().getDisplayMetrics().widthPixels;
            } catch (IllegalStateException e) {
                // Fragment已经detached，使用默认值
                Log.d("FloatingAnimation", "Fragment已detached，使用默认屏幕宽度");
                screenWidth = 1080; // 默认屏幕宽度
            }
            float startX = screenWidth + 100f; // 从屏幕右侧外100px的位置开始

            // 初始化问题位置和透明度
            question1.setAlpha(0f);
            question2.setAlpha(0f);
            question3.setAlpha(0f);
            question1.setTranslationX(startX);
            question2.setTranslationX(startX + 80f);  // 增加间距，让滑入更有层次
            question3.setTranslationX(startX + 160f);

            // 添加轻微的Y轴偏移，让动画更自然
            question1.setTranslationY(8f);
            question2.setTranslationY(12f);
            question3.setTranslationY(16f);

            // 依次显示问题，带延迟效果和弹性动画
            question1.animate()
                .alpha(1f)
                .translationX(0f)
                .translationY(0f)
                .setDuration(500)
                .setStartDelay(300)
                .setInterpolator(new android.view.animation.OvershootInterpolator(1.2f))
                .start();

            question2.animate()
                .alpha(1f)
                .translationX(0f)
                .translationY(0f)
                .setDuration(500)
                .setStartDelay(500)
                .setInterpolator(new android.view.animation.OvershootInterpolator(1.2f))
                .start();

            question3.animate()
                .alpha(1f)
                .translationX(0f)
                .translationY(0f)
                .setDuration(500)
                .setStartDelay(700)
                .setInterpolator(new android.view.animation.OvershootInterpolator(1.2f))
                .withEndAction(() -> {
                    // 等所有问题都显示完毕后再启动浮动动画
                    Log.d("FloatingAnimation", "所有问题显示完毕，开始启动浮动动画");
                    // 延迟启动浮动动画，确保入场动画完全结束
                    floatingHandler.postDelayed(() -> {
                        if (isAdded() && getActivity() != null &&
                            floatingQuestions.getVisibility() == android.view.View.VISIBLE) {
                            startBasicFloating();
                        }
                    }, 800); // 0.8秒后启动浮动动画
                })
                .start();
        }
    }

    /**
     * 发送问题并隐藏悬浮问题
     */
    private void sendQuestionAndHideFloating(String question) {
        // 标记已经使用过悬浮问题
        hasShownFloatingQuestions = true;

        // 隐藏悬浮问题
        hideFloatingQuestions();

        // 发送问题
        sendUserMessage(question);

        // 请求AI回答
        responseUserMessage();
    }

    /**
     * 隐藏悬浮问题（带优雅的滑出动画效果）
     */
    private void hideFloatingQuestions() {
        // 检查Fragment是否还存在且附加到Context
        if (!isAdded() || getActivity() == null) {
            Log.d("FloatingAnimation", "Fragment未附加到Context，取消隐藏动画");
            return;
        }

        // 标记已经使用过悬浮问题
        hasShownFloatingQuestions = true;

        // 停止浮动动画
        stopFloatingAnimation();

        if (floatingQuestions != null && floatingQuestions.getVisibility() == android.view.View.VISIBLE) {
            // 安全地获取屏幕宽度，滑出到屏幕右侧外
            int screenWidth;
            try {
                screenWidth = getResources().getDisplayMetrics().widthPixels;
            } catch (IllegalStateException e) {
                // Fragment已经detached，使用默认值
                Log.d("FloatingAnimation", "Fragment已detached，使用默认屏幕宽度");
                screenWidth = 1080; // 默认屏幕宽度
            }
            float endX = screenWidth + 100f; // 滑出到屏幕右侧外100px

            // 依次隐藏问题，带延迟效果
            question1.animate()
                .alpha(0f)
                .translationX(endX)
                .setDuration(350)
                .setStartDelay(0)
                .setInterpolator(new android.view.animation.AccelerateInterpolator())
                .start();

            question2.animate()
                .alpha(0f)
                .translationX(endX + 80f)
                .setDuration(350)
                .setStartDelay(100)
                .setInterpolator(new android.view.animation.AccelerateInterpolator())
                .start();

            question3.animate()
                .alpha(0f)
                .translationX(endX + 160f)
                .setDuration(350)
                .setStartDelay(200)
                .setInterpolator(new android.view.animation.AccelerateInterpolator())
                .withEndAction(() -> {
                    // 所有动画完成后隐藏容器
                    floatingQuestions.setVisibility(android.view.View.GONE);
                })
                .start();
        }
    }

    /**
     * 显示悬浮问题（带从右侧滑入的动画效果）
     */
    private void showFloatingQuestions() {
        // 检查Fragment是否还存在且附加到Context
        if (!isAdded() || getActivity() == null) {
            Log.d("FloatingAnimation", "Fragment未附加到Context，取消显示悬浮问题");
            return;
        }

        // 检查是否有问题数据
        if (robotDetail == null || robotDetail.getRobotQuestions() == null || robotDetail.getRobotQuestions().isEmpty()) {
            Log.d("FloatingAnimation", "没有问题数据，取消显示悬浮问题");
            return;
        }

        // 显示悬浮问题
        if (floatingQuestions != null && floatingQuestions.getVisibility() == android.view.View.GONE) {

            // 设置问题内容和点击事件
            List<String> questions = robotDetail.getRobotQuestions();
            TextView[] questionViews = {question1, question2, question3};

            // 使用循环设置问题
            for (int i = 0; i < Math.min(questions.size(), questionViews.length); i++) {
                TextView questionView = questionViews[i];
                if (questionView != null) {
                    String questionText = questions.get(i);
                    questionView.setText(questionText);
                    questionView.setOnClickListener(v -> {
                        sendQuestionAndHideFloating(questionText);
                    });
                }
            }
            floatingQuestions.setVisibility(android.view.View.VISIBLE);

            // 安全地获取屏幕宽度，从屏幕右侧外开始
            int screenWidth;
            try {
                screenWidth = getResources().getDisplayMetrics().widthPixels;
            } catch (IllegalStateException e) {
                // Fragment已经detached，使用默认值
                Log.d("FloatingAnimation", "Fragment已detached，使用默认屏幕宽度");
                screenWidth = 1080; // 默认屏幕宽度
            }
            float startX = screenWidth + 100f; // 从屏幕右侧外100px的位置开始

            // 初始化问题位置和透明度
            question1.setAlpha(0f);
            question2.setAlpha(0f);
            question3.setAlpha(0f);
            question1.setTranslationX(startX);
            question2.setTranslationX(startX + 80f);  // 增加间距，让滑入更有层次
            question3.setTranslationX(startX + 160f);

            // 添加轻微的Y轴偏移，让动画更自然
            question1.setTranslationY(8f);
            question2.setTranslationY(12f);
            question3.setTranslationY(16f);

            // 依次显示问题，带延迟效果和弹性动画
            question1.animate()
                .alpha(1f)
                .translationX(0f)
                .translationY(0f)
                .setDuration(500)
                .setStartDelay(300)
                .setInterpolator(new android.view.animation.OvershootInterpolator(1.2f))
                .start();

            question2.animate()
                .alpha(1f)
                .translationX(0f)
                .translationY(0f)
                .setDuration(500)
                .setStartDelay(500)
                .setInterpolator(new android.view.animation.OvershootInterpolator(1.2f))
                .start();

            question3.animate()
                .alpha(1f)
                .translationX(0f)
                .translationY(0f)
                .setDuration(500)
                .setStartDelay(700)
                .setInterpolator(new android.view.animation.OvershootInterpolator(1.2f))
                .withEndAction(() -> {
                    // 等所有问题都显示完毕后再启动浮动动画
                    Log.d("FloatingAnimation", "所有问题显示完毕，开始启动浮动动画");
                    // 延迟启动浮动动画，确保入场动画完全结束
                    floatingHandler.postDelayed(() -> {
                        if (isAdded() && getActivity() != null &&
                            floatingQuestions.getVisibility() == android.view.View.VISIBLE &&
                            !hasShownFloatingQuestions) {
                            startBasicFloating();
                        }
                    }, 800); // 0.8秒后启动浮动动画
                })
                .start();
        }
    }

    // 浮动动画的Handler
    private android.os.Handler floatingHandler = new android.os.Handler(android.os.Looper.getMainLooper());
    private Runnable floatingRunnable;
    private boolean isFloatingAnimationRunning = false;

    /**
     * 启动优化的浮动动画（减小浮动范围，避免遮挡）
     */
    private void startBasicFloating() {
        Log.d("FloatingAnimation", "启动基础浮动动画");

        // 如果动画已经在运行，先停止
        stopFloatingAnimation();

        isFloatingAnimationRunning = true;

        // 使用Handler替代Timer，避免内存泄漏
        floatingRunnable = new Runnable() {
            boolean goingUp = true;

            @Override
            public void run() {
                // 检查Fragment是否还存在且View可见
                if (!isAdded() || getActivity() == null ||
                    floatingQuestions.getVisibility() != android.view.View.VISIBLE ||
                    !isFloatingAnimationRunning) {
                    return;
                }

                // 进一步减小浮动范围，避免顶部问题框被遮挡
                // 向上浮动范围从-15f减小到-8f，确保顶部问题框不会消失
                float targetY = goingUp ? -8f : 0f;

                // 确保View存在且可见
                if (question1 != null && question1.getVisibility() == android.view.View.VISIBLE) {
                    question1.animate()
                        .translationY(targetY * 0.5f) // 顶部问题框浮动最小，只有-4f
                        .setDuration(1500)
                        .setInterpolator(new android.view.animation.AccelerateDecelerateInterpolator())
                        .start();
                }

                if (question2 != null && question2.getVisibility() == android.view.View.VISIBLE) {
                    question2.animate()
                        .translationY(targetY * 0.8f) // 中间问题框适中浮动，-6.4f
                        .setDuration(1800)
                        .setInterpolator(new android.view.animation.AccelerateDecelerateInterpolator())
                        .start();
                }

                if (question3 != null && question3.getVisibility() == android.view.View.VISIBLE) {
                    question3.animate()
                        .translationY(targetY) // 底部问题框正常浮动，-8f
                        .setDuration(1200)
                        .setInterpolator(new android.view.animation.AccelerateDecelerateInterpolator())
                        .start();
                }

                goingUp = !goingUp;

                // 继续下一次动画，增加间隔时间让动画更优雅
                if (isFloatingAnimationRunning) {
                    floatingHandler.postDelayed(this, 2500);
                }
            }
        };

        // 启动动画
        floatingHandler.post(floatingRunnable);
    }

    /**
     * 停止浮动动画
     */
    private void stopFloatingAnimation() {
        isFloatingAnimationRunning = false;
        if (floatingHandler != null && floatingRunnable != null) {
            floatingHandler.removeCallbacks(floatingRunnable);
        }
    }



    @Override
    public void onPause() {
        super.onPause();
        // 暂停时停止动画
        stopFloatingAnimation();
    }

    @Override
    public void onResume() {
        super.onResume();
        // 恢复时重新启动动画（如果需要）
        if (!hasShownFloatingQuestions &&
            floatingQuestions != null &&
            floatingQuestions.getVisibility() == android.view.View.VISIBLE) {
            floatingHandler.postDelayed(() -> {
                if (isAdded() && getActivity() != null) {
                    startBasicFloating();
                }
            }, 500);
        }
    }



    /**
     * 加载机器人信息
     */
    private void loadRobotInfo() {
        if (hasLoadedRobotInfo) {
            return;
        }

        // 从模块参数中获取机器人ID
        String robotIdStr = moduleVo.getMoudleParams().trim();
        Long robotId;
        try {
            robotId = Long.parseLong(robotIdStr);
        } catch (NumberFormatException e) {
            ToastUtils.toast("机器人ID格式错误: " + robotIdStr);
            return;
        }

        // 调用API获取机器人详情
        XHttpProxy.proxy(ApiService.IRobotService.class)
                .getRobotDetail(robotId)
                .subscribe(new TipRequestSubscriber<RobotDetailVo>() {
                    @Override
                    protected void onSuccess(RobotDetailVo response) {
                        robotDetail = response;
                        hasLoadedRobotInfo = true;

                        // 更新标题为机器人名称
             /*           if (robotDetail.getRobotName() != null && !robotDetail.getRobotName().trim().isEmpty()) {
                            getActivity().runOnUiThread(() -> {
                                try {
                                    if (getActivity() != null && titleBar != null) {
                                        titleBar.setTitle(robotDetail.getRobotName());
                                    }
                                } catch (Exception e) {
                                    Log.e("ChatbotFragment", "更新标题失败: " + e.getMessage());
                                }
                            });
                        }*/

                        // 发送开场白
                        sendGreetingMessage();
                    }
                });
    }

    /**
     * 发送开场白消息
     */
    private void sendGreetingMessage() {
        if (robotDetail != null && robotDetail.getRobotGreeting() != null && !robotDetail.getRobotGreeting().trim().isEmpty()) {
            // 直接显示机器人的开场白，不需要AI生成
            sendAiMessage(robotDetail.getRobotGreeting());

            // 开场白显示完成后，自动显示悬浮问题
            getActivity().runOnUiThread(() -> {
                if (isFirstAIResponse && !hasShownFloatingQuestions &&
                        floatingQuestions != null &&
                        floatingQuestions.getVisibility() == android.view.View.GONE) {
                    Log.d("FloatingAnimation", "开场白显示完成，自动显示悬浮问题");
                    showFloatingQuestions();
                }
                // 标记第一次AI回答已完成
                isFirstAIResponse = false;
            });
        }
        // 如果没有开场白，但有问题，也自动显示悬浮问题
        else if (robotDetail != null && robotDetail.getRobotQuestions() != null && !robotDetail.getRobotQuestions().isEmpty()) {
            getActivity().runOnUiThread(() -> {
                if (!hasShownFloatingQuestions) {
                    showFloatingQuestions();
                }
            });
        }
    }





    /**
     * 发送隐藏的用户消息（不在界面显示）
     */
    private void sendHiddenUserMessage(String message) {
        // 只构造消息数据，不添加到界面显示
        User user = new User("1", "我", "http://oss.geyuantz.cn/module/headImg.png");
        IMsg msg = new IMsg(message, IMessage.MessageType.SEND_TEXT);
        msg.setUserInfo(user);
        // 直接添加到适配器的消息列表中，但不刷新界面
        msgAdapter.getMessageList().add(0, msg);
    }


    /**
     * 更新消息
     */
    public void updateMessage(IMsg msg) {
        //在主线程更新消息
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                msgAdapter.updateMessage(msg);
                //滚动到最底部
                messageList.scrollBy(0, 100);
            }
        });
    }

    /**
     * Ai发送消息
     */
    public IMsg sendAiMessage(String message) {
        // 优先使用机器人的头像和名称，如果没有则使用默认值
        String aiAvatarUrl;
        String aiName;

        if (robotDetail != null) {
            // 使用机器人的头像，如果没有则使用默认头像
            if (robotDetail.getRobotAvatar() != null && !robotDetail.getRobotAvatar().trim().isEmpty()) {
                aiAvatarUrl = robotDetail.getRobotAvatar();
            } else {
                aiAvatarUrl = "android.resource://" + getContext().getPackageName() + "/" + R.drawable.robot;
            }

            // 使用机器人的名称，如果没有则使用模块名称
            if (robotDetail.getRobotName() != null && !robotDetail.getRobotName().trim().isEmpty()) {
                aiName = robotDetail.getRobotName();
            } else {
                aiName = moduleVo.getModuleName();
            }
        } else {
            // 如果机器人信息还没加载，使用默认值
            aiAvatarUrl = "android.resource://" + getContext().getPackageName() + "/" + R.drawable.robot;
            aiName = moduleVo.getModuleName();
        }

        User ai = new User("2", aiName, aiAvatarUrl);
        IMsg msg = new IMsg(message, IMessage.MessageType.RECEIVE_TEXT);
        msg.setUserInfo(ai);
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                msgAdapter.addToStart(msg, true);
            }
        });
        return msg;
    }

    /**
     * 用户发送消息
     */
    public void sendUserMessage(String message) {
        // 使用user.png作为用户头像
        String userAvatarUrl = "android.resource://" + getContext().getPackageName() + "/" + R.drawable.user;
        User user = new User("1", "我", userAvatarUrl);
        IMsg msg = new IMsg(message, IMessage.MessageType.SEND_TEXT);
        msg.setUserInfo(user);
        msgAdapter.addToStart(msg, true);
    }

    /**
     * 响应用户消息
     */
    public void responseUserMessage() {
        AIBo aiBo = new AIBo();
        // 设置机器人ID为Long类型
        try {
            Long robotId = Long.parseLong(moduleVo.getMoudleParams().trim());
            aiBo.setRobotId(robotId);
        } catch (NumberFormatException e) {
            // 如果解析失败，使用默认值1
            ToastUtils.toast("机器人ID格式错误");
        }
        List<AIBo.ChatMessage> chatMessages = new ArrayList<>();
        List<IMsg> messageList = msgAdapter.getMessageList();
        // 获取的消息是倒序的，需要反转
        Collections.reverse(messageList);
        for (IMsg iMsg : messageList) {
            AIBo.ChatMessage chatMessage = new AIBo.ChatMessage();
            chatMessage.setRole(Integer.parseInt(iMsg.getFromUser().getId()));
            chatMessage.setContent(iMsg.getText());
            chatMessages.add(chatMessage);
            Log.d("EventSource ~", "add: " + chatMessage);
        }
        aiBo.setChatMessages(chatMessages);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), new Gson().toJson(aiBo));
        // 定义see接口
        Request request = new Request.Builder()
                .header("authorization", TokenUtils.getToken())
                .url(XHttp.getBaseUrl() + "ai/ask")
                .post(requestBody)
                .build();
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.DAYS)
                .readTimeout(1, TimeUnit.DAYS)//这边需要将超时显示设置长一点，不然刚连上就断开，之前以为调用方式错误被坑了半天
                .build();
        // 发送空白消息,用于后续更新
        IMsg msg = sendAiMessage("");
        // 实例化EventSource，注册EventSource监听器
        RealEventSource realEventSource = new RealEventSource(request, new EventSourceListener() {
            private long callStartNanos;

            private void printEvent(String name) {
                long nowNanos = System.nanoTime();
                if (name.equals("callStart")) {
                    callStartNanos = nowNanos;
                }
                long elapsedNanos = nowNanos - callStartNanos;
//                Log.d("EventSource ~", String.format("EventSource %s in %.1fms%n", name, elapsedNanos / 1000000d));
            }

            @Override
            public void onOpen(EventSource eventSource, Response response) {
                printEvent("onOpen");
            }

            @Override
            public void onEvent(EventSource eventSource, String id, String type, String data) {
                printEvent("onEvent");
                Log.d("EventSource ~", "接收消息: " + data);
                // {"msg":"你好"}
                //解析json中msg的数据
                String content = JSONUtils.getString(data, "msg", "");
                msg.setText(msg.getText() + content);
                updateMessage(msg);
            }

            @Override
            public void onClosed(EventSource eventSource) {
                printEvent("onClosed");
                // 检查Fragment是否还存在且附加到Context
                if (!isAdded() || getActivity() == null) {
                    Log.d("FloatingAnimation", "Fragment已销毁，取消显示悬浮问题");
                }else{
                    getActivity().runOnUiThread(() -> {
                        // 在第一次AI回答完成且悬浮问题未显示时自动显示
                        if (isFirstAIResponse && !hasShownFloatingQuestions &&
                                floatingQuestions != null &&
                                floatingQuestions.getVisibility() == android.view.View.GONE &&
                                robotDetail != null && robotDetail.getRobotQuestions() != null && !robotDetail.getRobotQuestions().isEmpty()) {
                            Log.d("FloatingAnimation", "第一次AI回答完成，自动显示悬浮问题");
                            showFloatingQuestions();
                        }
                        // 标记第一次AI回答已完成
                        isFirstAIResponse = false;
                    });
                }
            }

            @Override
            public void onFailure(EventSource eventSource, Throwable t, Response response) {
                printEvent("onFailure");//这边可以监听并重新打开
                // 打印错误信息
                t.printStackTrace();
            }
        });
        realEventSource.connect(okHttpClient);//真正开始请求的一步
    }

    @Override
    public void onDestroyView() {
        // 停止所有动画，防止内存泄漏
        stopFloatingAnimation();

        // 清除所有View动画
        if (question1 != null) {
            question1.clearAnimation();
            question1.animate().cancel();
        }
        if (question2 != null) {
            question2.clearAnimation();
            question2.animate().cancel();
        }
        if (question3 != null) {
            question3.clearAnimation();
            question3.animate().cancel();
        }

        // 清理浮动动画Handler
        if (floatingHandler != null) {
            floatingHandler.removeCallbacksAndMessages(null);
        }

        super.onDestroyView();
    }
}