package cn.geyuantz.reader.fragment.video;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import cn.geyuantz.reader.adapter.reader.ModernCategoryAdapter;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.subscriber.NoTipRequestSubscriber;
import cn.geyuantz.reader.core.http.vo.GClassVo;
import cn.geyuantz.reader.databinding.FragmentVideoCategoryBinding;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xui.widget.actionbar.TitleBar;

import java.util.ArrayList;
import java.util.List;

@Page
public class VideoCategoryFragment extends BaseFragment<FragmentVideoCategoryBinding> {
    private ModernCategoryAdapter mCategoryAdapter;

    @Override
    protected TitleBar initTitle() {
        return super.initTitle().setTitle("视频分类");
    }

    @Override
    protected void initViews() {
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerView.setAdapter(mCategoryAdapter = new ModernCategoryAdapter());
        binding.refreshLayout.autoRefresh();
    }

    @Override
    protected void initListeners() {
        binding.refreshLayout.setOnRefreshListener(refreshLayout -> {
            // 直接加载服务器数据
            XHttpProxy.proxy(ApiService.IClassService.class)
                    .getClassList(3L)
                    .subscribe(new NoTipRequestSubscriber<List<GClassVo>>() {
                                   @Override
                                   protected void onSuccess(List<GClassVo> gClassVos) {
                                       mCategoryAdapter.loadMore(gClassVos);
                                       binding.refreshLayout.finishRefresh();
                                   }
                               }
                    );
        });

        mCategoryAdapter.setOnItemClickListener((itemView, item, position) -> {
            openPage(VideoListFragment.class, "classVo", item);
        });
    }


    @NonNull
    @Override
    protected FragmentVideoCategoryBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentVideoCategoryBinding.inflate(inflater, container, attachToRoot);
    }


}