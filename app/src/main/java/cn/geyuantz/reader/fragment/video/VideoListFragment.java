package cn.geyuantz.reader.fragment.video;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import cn.geyuantz.reader.adapter.reader.ModernVideoListAdapter;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.bo.PageQuery;
import cn.geyuantz.reader.core.http.bo.RVideoList;
import cn.geyuantz.reader.core.http.subscriber.NoTipRequestSubscriber;
import cn.geyuantz.reader.core.http.vo.GClassVo;
import cn.geyuantz.reader.core.http.vo.GVideoVo;
import cn.geyuantz.reader.databinding.FragmentVideoListBinding;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xui.widget.actionbar.TitleBar;

import java.util.List;

@Page
public class VideoListFragment extends BaseFragment<FragmentVideoListBinding> {

    private GClassVo mClassVo;
    private int mPage = -1;
    private ModernVideoListAdapter videoListAdapter;

    @Override
    protected TitleBar initTitle() {
        return super.initTitle().setTitle(mClassVo.getClassName());
    }

    @NonNull
    @Override
    protected FragmentVideoListBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentVideoListBinding.inflate(inflater, container, attachToRoot);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mClassVo = (GClassVo) getArguments().getSerializable("classVo");
    }

    @Override
    protected void initViews() {
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerView.setAdapter(videoListAdapter = new ModernVideoListAdapter());
        binding.refreshLayout.autoRefresh();

    }
    @Override
    protected void initListeners() {
        binding.refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                mPage++;
                fetchVodeoList(mPage, false, refreshLayout);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                mPage = 1;
                fetchVodeoList(mPage, true, refreshLayout);
            }
        });

        videoListAdapter.setOnItemClickListener((itemView, item, position) -> {
                openNewPage(VideoPlayListFragment.class, "videoVo", item);
        });

    }

    private void fetchVodeoList(int mPage, boolean isRefresh, RefreshLayout refreshLayout) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(mPage);
        RVideoList rVideoList = new RVideoList().setPageQuery(pageQuery).setClassId(mClassVo.getClassId());
        XHttpProxy.proxy(ApiService.IVideoService.class)
                .getVideoList(rVideoList)
                .subscribe(
                        new NoTipRequestSubscriber<List<GVideoVo>>() {
                            @Override
                            protected void onSuccess(List<GVideoVo> gAudioVos) {
                                if (isRefresh) {
                                    videoListAdapter.refresh(gAudioVos);
                                    refreshLayout.finishRefresh();
                                } else {
                                    videoListAdapter.loadMore(gAudioVos);
                                    refreshLayout.finishLoadMore();
                                }
                            }
                        }
                );
    }

}