package cn.geyuantz.reader.fragment.audio;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import cn.geyuantz.reader.adapter.reader.ModernAudioCategoryAdapter;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.subscriber.NoTipRequestSubscriber;
import cn.geyuantz.reader.core.http.vo.GClassVo;
import cn.geyuantz.reader.databinding.FragmentVideoCategoryBinding;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xui.widget.actionbar.TitleBar;

import java.util.List;

@Page
public class AudioCategoryFragment extends BaseFragment<FragmentVideoCategoryBinding> {
    private ModernAudioCategoryAdapter mCategoryAdapter;

    @Override
    protected TitleBar initTitle() {
        return super.initTitle().setTitle("音频分类");
    }

    @Override
    protected void initViews() {
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerView.setAdapter(mCategoryAdapter = new ModernAudioCategoryAdapter());
        binding.refreshLayout.autoRefresh();
    }

    @Override
    protected void initListeners() {
        binding.refreshLayout.setOnRefreshListener(refreshLayout -> {
            XHttpProxy.proxy(ApiService.IClassService.class)
                    .getClassList(2L)
                    .subscribe(new NoTipRequestSubscriber<List<GClassVo>>() {
                                   @Override
                                   protected void onSuccess(List<GClassVo> gClassVos) {
                                       mCategoryAdapter.loadMore(gClassVos);
                                       binding.refreshLayout.finishRefresh();
                                   }
                               }
                    );
        });

        mCategoryAdapter.setOnItemClickListener((itemView, item, position) -> {
            openNewPage(AudioListFragment.class, "classVo", item);
        });
    }

    @NonNull
    @Override
    protected FragmentVideoCategoryBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentVideoCategoryBinding.inflate(inflater, container, attachToRoot);
    }


}