/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.fragment.setting;

import android.os.Build;
import android.text.InputType;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import cn.geyuantz.reader.MyApp;
import cn.geyuantz.reader.core.BaseFragment;
import cn.geyuantz.reader.R;
import cn.geyuantz.reader.core.http.api.ApiService;
import cn.geyuantz.reader.core.http.bo.RRequestHelp;
import cn.geyuantz.reader.core.http.subscriber.TipRequestSubscriber;
import cn.geyuantz.reader.core.http.vo.DeviceInfoVo;
import cn.geyuantz.reader.databinding.FragmentSettingsBinding;
import com.xuexiang.xaop.annotation.SingleClick;
import com.xuexiang.xhttp2.XHttpProxy;
import com.xuexiang.xpage.annotation.Page;
import com.xuexiang.xui.utils.XToastUtils;
import com.xuexiang.xui.widget.dialog.DialogLoader;
import com.xuexiang.xui.widget.dialog.materialdialog.MaterialDialog;
import com.xuexiang.xui.widget.textview.supertextview.SuperTextView;
import com.xuexiang.xupdate.utils.UpdateUtils;
import com.xuexiang.xutil.data.SPUtils;
import com.xuexiang.xutil.tip.ToastUtils;

/**
 * <AUTHOR>
 * @since 2019-10-15 22:38
 */
@Page(name = "管理页面")
public class SettingsFragment extends BaseFragment<FragmentSettingsBinding> implements SuperTextView.OnSuperTextViewClickListener {

    @NonNull
    @Override
    protected FragmentSettingsBinding viewBindingInflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, boolean attachToRoot) {
        return FragmentSettingsBinding.inflate(inflater, container, attachToRoot);
    }

    @Override
    protected void initViews() {
        binding.menuDeviceInfo.setOnSuperTextViewClickListener(this);
        binding.requestHelp.setOnSuperTextViewClickListener(this);
    }

    @SingleClick
    @Override
    public void onClick(SuperTextView superTextView) {
        int id = superTextView.getId();
        if (id == R.id.menu_device_info) {
            openDeviceInfoDialog();
        }else if (id == R.id.request_help) {
             openRequestHelpDialog();
        }
    }

    private void openRequestHelpDialog() {
        //让用户输入反馈信息和联系方式
        new MaterialDialog.Builder(getContext())
                .iconRes(R.drawable.ic_password)
                .title("请求协助")
                .content("请输入您的问题和联系方式")
                .inputType(InputType.TYPE_CLASS_TEXT)
                .input(
                        "请输入您的问题和联系方式",
                        "",
                        false,
                        ((dialog, input) -> {}))
                .onPositive(((dialog, which) -> {
                    String content = dialog.getInputEditText().getText().toString();
                    if (content.isEmpty()) {
                        ToastUtils.toast("请输入您的问题和联系方式");
                    } else {
                        RRequestHelp rRequestHelp = new RRequestHelp();
                        rRequestHelp.setSoftwareCode(MyApp.softwareCode);
                        rRequestHelp.setDeviceInfo(generateDeviceInfo());
                        rRequestHelp.setRemark(content);
                        //提交请求
                        XHttpProxy.proxy(ApiService.IDeviceService.class)
                                .requestHelp(rRequestHelp)
                                .subscribe(new TipRequestSubscriber<String>() {
                                    @Override
                                    protected void onSuccess(String s) {
                                        XToastUtils.success(s);
                                    }
                                });
                    }

                }))
                .cancelable(true)
                .show();
    }

    private void openDeviceInfoDialog() {
        //获取设备信息
        DeviceInfoVo deviceInfo = SPUtils.getObject(SPUtils.getDefaultSharedPreferences(), "deviceInfo", DeviceInfoVo.class);
        DialogLoader.getInstance().showTipDialog(
          getContext(),
          "设备信息",
            generateDeviceInfo(),
                "确定"
        );
    }

    /**
     * 生成设备信息文本
     */
    private String generateDeviceInfo() {
        DeviceInfoVo deviceInfo = SPUtils.getObject(SPUtils.getDefaultSharedPreferences(), "deviceInfo", DeviceInfoVo.class);

        return "设备ID：" + deviceInfo.getDeviceId() + "\n" +
                "设备名称：" + deviceInfo.getDeviceTitle() + "\n" +
                "风格ID：" + deviceInfo.getStyleId() + "\n" +
                "软件版本： " + UpdateUtils.getVersionCode(getContext()) + "\n" +
                "SDK版本: " + Build.VERSION.SDK_INT + "\n" +
                "安卓版本：" + Build.VERSION.RELEASE;
    }

}
