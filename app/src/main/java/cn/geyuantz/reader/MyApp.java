/*
 * Copyright (C) 2024 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.res.Configuration;

import androidx.annotation.NonNull;
import androidx.multidex.MultiDex;

import com.scwang.smartrefresh.header.WaterDropHeader;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.DefaultRefreshFooterCreator;
import com.scwang.smartrefresh.layout.api.DefaultRefreshHeaderCreator;
import com.scwang.smartrefresh.layout.api.RefreshFooter;
import com.scwang.smartrefresh.layout.api.RefreshHeader;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.footer.BallPulseFooter;
import com.scwang.smartrefresh.layout.footer.ClassicsFooter;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;
import com.shuyu.gsyvideoplayer.player.IjkPlayerManager;
import cn.geyuantz.reader.utils.sdkinit.XBasicLibInit;
import cn.geyuantz.reader.utils.sdkinit.ANRWatchDogInit;
import cn.geyuantz.reader.utils.sdkinit.XUpdateInit;

import me.jessyan.autosize.AutoSizeConfig;
import me.jessyan.autosize.onAdaptListener;
import me.jessyan.autosize.utils.ScreenUtils;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;

/**
 * <AUTHOR>
 * @since 2018/11/7 下午1:12
 */
public class MyApp extends Application {

    public static String softwareCode = "reader";
    public static Boolean isLand = false;

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        //解决4.x运行崩溃的问题
        MultiDex.install(this);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        // 只初始化关键的库，其他的延迟到后台线程
        initCriticalLibs();
        //处理autoSize
        initAutoSize();

        // 延迟初始化非关键组件
        initNonCriticalLibsAsync();
    }

    private void initAutoSize() {
        //屏幕适配监听器
        AutoSizeConfig.getInstance().setOnAdaptListener(new onAdaptListener() {
            @Override
            public void onAdaptBefore(Object target, Activity activity) {
                AutoSizeConfig.getInstance().setScreenWidth(ScreenUtils.getScreenSize(activity)[0]);
                AutoSizeConfig.getInstance().setScreenHeight(ScreenUtils.getScreenSize(activity)[1]);
                //根据屏幕方向，设置适配基准
                if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    //设置横屏基准
                    AutoSizeConfig.getInstance()
                            .setDesignWidthInDp(640)
                            .setDesignHeightInDp(360);
                    isLand = true;
                } else {
                    //设置竖屏基准
                    AutoSizeConfig.getInstance()
                            .setDesignWidthInDp(360)
                            .setDesignHeightInDp(640);
                    isLand = false;
                }
            }

            @Override
            public void onAdaptAfter(Object target, Activity activity) {

            }
        });
    }

    /**
     * 初始化关键库（启动时必需）
     */
    private void initCriticalLibs() {
        // X系列基础库初始化（必需）
        XBasicLibInit.init(this);
    }

    /**
     * 异步初始化非关键库（可延迟）
     */
    private void initNonCriticalLibsAsync() {
        // 在后台线程初始化非关键组件
        new Thread(() -> {
            try {
                // 版本更新初始化
                XUpdateInit.init(this);
                // ANR监控
                ANRWatchDogInit.init();
                //初始化GSYVideoPlayer
                GSYVideoPlayerInit();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }


    private void GSYVideoPlayerInit() {
        //ijk关闭log
        IjkPlayerManager.setLogLevel(IjkMediaPlayer.IJK_LOG_SILENT);
    }

    /**
     * @return 当前app是否是调试开发模式
     */
    public static boolean isDebug() {
        return BuildConfig.DEBUG;
    }

    //static 代码段可以防止内存泄露
    static {
        //设置全局的Header构建器
        SmartRefreshLayout.setDefaultRefreshHeaderCreator(new DefaultRefreshHeaderCreator() {
            @NonNull
            @Override
            public RefreshHeader createRefreshHeader(@NonNull Context context, @NonNull RefreshLayout layout) {
                return new WaterDropHeader(context);//.setTimeFormat(new DynamicTimeFormat("更新于 %s"));//指定为经典Header，默认是 贝塞尔雷达Header
            }
        });
        //设置全局的Footer构建器
        SmartRefreshLayout.setDefaultRefreshFooterCreator(new DefaultRefreshFooterCreator() {
            @NonNull
            @Override
            public RefreshFooter createRefreshFooter(@NonNull Context context, @NonNull RefreshLayout layout) {
                //指定为经典Footer，默认是 BallPulseFooter
                return new BallPulseFooter(context);
            }
        });
    }

}
