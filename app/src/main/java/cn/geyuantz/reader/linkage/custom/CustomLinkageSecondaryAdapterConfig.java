/*
 * Copyright (C) 2019 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package cn.geyuantz.reader.linkage.custom;


import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.kunminx.linkage.adapter.viewholder.LinkageSecondaryHeaderViewHolder;
import com.kunminx.linkage.adapter.viewholder.LinkageSecondaryViewHolder;
import com.kunminx.linkage.bean.BaseGroupedItem;
import com.kunminx.linkage.contract.ILinkageSecondaryAdapterConfig;
import cn.geyuantz.reader.R;
import com.xuexiang.xui.utils.DensityUtils;
import com.xuexiang.xui.utils.DrawableUtils;
import com.xuexiang.xui.widget.textview.supertextview.SuperTextView;


/**
 * <AUTHOR>
 * @since 2019-11-25 17:17
 */
public class CustomLinkageSecondaryAdapterConfig implements ILinkageSecondaryAdapterConfig<CustomGroupedItem.ItemInfo> {

    private OnSecondaryItemClickListener mItemClickListener;
    private static final int SPAN_COUNT = 2;
    private Context mContext;

    public CustomLinkageSecondaryAdapterConfig(OnSecondaryItemClickListener itemClickListener, Context context) {
        this.mContext = context;
        mItemClickListener = itemClickListener;
    }

    public CustomLinkageSecondaryAdapterConfig setOnItemClickListener(OnSecondaryItemClickListener itemClickListener) {
        mItemClickListener = itemClickListener;
        return this;
    }

    @Override
    public void setContext(Context context) {
    }



    @Override
    public int getLinearLayoutId() {
        return R.layout.newspaper_adapter_linkage_secondary_linear;
    }

    @Override
    public int getHeaderLayoutId() {
        return R.layout.default_adapter_linkage_secondary_header;
    }



    @Override
    public int getHeaderTextViewId() {
        return R.id.secondary_header;
    }

    @Override
    public int getSpanCountOfGridMode() {
        return SPAN_COUNT;
    }

    @Override
    public void onBindViewHolder(final LinkageSecondaryViewHolder holder,
                                 final BaseGroupedItem<CustomGroupedItem.ItemInfo> item) {
        SuperTextView superTextView = holder.getView(R.id.tv_title);
        TextView iconTextView = holder.getView(R.id.tv_icon);
        String newspaperTitle = item.info.getTitle();

        // 设置报纸标题
        superTextView.setCenterString(newspaperTitle);

        // 设置图标文字（取第一个字符）
        String iconText = newspaperTitle.length() > 0 ? newspaperTitle.substring(0, 1) : "📰";
        iconTextView.setText(iconText);

        // 设置点击事件
        ViewGroup viewGroup = holder.getView(R.id.level_2_item);
        viewGroup.setOnClickListener(v -> {
            if (mItemClickListener != null) {
                mItemClickListener.onSecondaryItemClick(holder, viewGroup, item);
            }
        });

    }

    @Override
    public void onBindHeaderViewHolder(LinkageSecondaryHeaderViewHolder holder,
                                       BaseGroupedItem<CustomGroupedItem.ItemInfo> item) {
        ((TextView) holder.getView(R.id.secondary_header)).setText(item.header);
    }



    public interface OnSecondaryItemClickListener {
        /**
         * we suggest you get position by holder.getAdapterPosition
         *
         * @param holder primaryHolder
         * @param item   内容
         */
        void onSecondaryItemClick(LinkageSecondaryViewHolder holder, ViewGroup view, BaseGroupedItem<CustomGroupedItem.ItemInfo> item);
    }
}
