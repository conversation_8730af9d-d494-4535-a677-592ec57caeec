package cn.geyuantz.reader.manager;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;

import com.shuyu.gsyvideoplayer.GSYVideoManager;
import com.shuyu.gsyvideoplayer.builder.GSYVideoOptionBuilder;
import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack;
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer;

import java.util.List;

import cn.geyuantz.reader.core.http.vo.GAudioVo;

public class AudioPlayerManager {
    private static final String TAG = "AudioPlayerManager";
    private static AudioPlayerManager instance;

    private StandardGSYVideoPlayer gsyVideoPlayer;
    private Context context;
    private List<GAudioVo> audioList;
    private int currentIndex = 0;
    private boolean isPlaying = false;
    private Handler progressHandler;
    private Runnable progressRunnable;
    
    private OnAudioPlayerListener listener;
    
    public interface OnAudioPlayerListener {
        void onPlayStateChanged(boolean isPlaying);
        void onProgressUpdate(int currentPosition, int duration);
        void onAudioChanged(GAudioVo audio, int index);
        void onError(String error);
        void onNeedLoadMore(OnLoadMoreCallback callback); // 需要加载更多数据的回调，带回调接口
    }

    /**
     * 加载更多完成的回调接口
     */
    public interface OnLoadMoreCallback {
        void onLoadMoreComplete(boolean hasMoreData);
    }
    
    private AudioPlayerManager(Context context) {
        this.context = context.getApplicationContext();
        this.progressHandler = new Handler(Looper.getMainLooper());
    }

    public void initWithGSYVideoPlayer(StandardGSYVideoPlayer player) {
        this.gsyVideoPlayer = player;
        initGSYVideoPlayer();
    }
    
    public static synchronized AudioPlayerManager getInstance(Context context) {
        if (instance == null) {
            instance = new AudioPlayerManager(context);
        }
        return instance;
    }
    
    private void initGSYVideoPlayer() {
        if (gsyVideoPlayer == null) return;

        // 配置GSYVideoPlayer用于音频播放
        GSYVideoOptionBuilder gsyVideoOption = new GSYVideoOptionBuilder();
        gsyVideoOption
                .setIsTouchWiget(true)
                .setRotateViewAuto(false)
                .setLockLand(false)
                .setAutoFullWithSize(false)
                .setShowFullAnimation(false)
                .setNeedLockFull(false)
                .setCacheWithPlay(false)
                .setVideoAllCallBack(new GSYSampleCallBack() {
                    @Override
                    public void onPrepared(String url, Object... objects) {
                        super.onPrepared(url, objects);
                        Log.d(TAG, "GSYVideoPlayer prepared");
                        // 准备完成后启动进度更新
                        if (isPlaying) {
                            progressHandler.post(progressRunnable);
                        }
                        if (listener != null) {
                            long duration = gsyVideoPlayer.getDuration();
                            listener.onProgressUpdate(0, (int) duration);
                        }
                    }

                    @Override
                    public void onAutoComplete(String url, Object... objects) {
                        super.onAutoComplete(url, objects);
                        Log.d(TAG, "Audio completed");
                        isPlaying = false;
                        progressHandler.removeCallbacks(progressRunnable);
                        playNext();
                    }

                    @Override
                    public void onPlayError(String url, Object... objects) {
                        super.onPlayError(url, objects);
                        Log.e(TAG, "GSYVideoPlayer error");
                        isPlaying = false;
                        progressHandler.removeCallbacks(progressRunnable);
                        if (listener != null) {
                            listener.onError("播放出错");
                        }
                    }
                })
                .build(gsyVideoPlayer);

        // 隐藏视频相关的UI控件，只保留音频播放功能
        gsyVideoPlayer.getBackButton().setVisibility(View.GONE);
        gsyVideoPlayer.getFullscreenButton().setVisibility(View.GONE);

        setupProgressUpdater();
    }
    
    private void setupProgressUpdater() {
        progressRunnable = new Runnable() {
            @Override
            public void run() {
                if (gsyVideoPlayer != null && isPlaying) {
                    try {
                        // 使用正确的方法获取当前播放位置和总时长
                        long currentPosition = gsyVideoPlayer.getCurrentPositionWhenPlaying();
                        long duration = gsyVideoPlayer.getDuration();

                        if (listener != null && duration > 0) {
                            listener.onProgressUpdate((int) currentPosition, (int) duration);
                        }
                        progressHandler.postDelayed(this, 1000);
                    } catch (Exception e) {
                        Log.e(TAG, "Error updating progress", e);
                    }
                }
            }
        };
    }
    
    public void setAudioList(List<GAudioVo> audioList) {
        setAudioList(audioList, true);
    }

    public void setAudioList(List<GAudioVo> audioList, boolean resetIndex) {
        this.audioList = audioList;
        if (resetIndex) {
            this.currentIndex = 0;
        }
    }
    
    public void playAudio(GAudioVo audio) {
        if (audioList != null) {
            for (int i = 0; i < audioList.size(); i++) {
                if (audioList.get(i).getAudioId().equals(audio.getAudioId())) {
                    currentIndex = i;
                    break;
                }
            }
        }
        playCurrentAudio();
    }
    
    public void playCurrentAudio() {
        if (audioList == null || currentIndex >= audioList.size() || gsyVideoPlayer == null) {
            return;
        }

        GAudioVo currentAudio = audioList.get(currentIndex);
        try {
            // 使用GSYVideoPlayer播放音频
            gsyVideoPlayer.setUp(currentAudio.getAudioUrl(), false, currentAudio.getAudioName());
            gsyVideoPlayer.startPlayLogic();

            // 设置播放状态
            isPlaying = true;

            // 启动进度更新
            progressHandler.post(progressRunnable);

            if (listener != null) {
                listener.onAudioChanged(currentAudio, currentIndex);
                listener.onPlayStateChanged(isPlaying);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error setting data source", e);
            if (listener != null) {
                listener.onError("无法加载音频文件");
            }
        }
    }
    
    public void togglePlayPause() {
        if (gsyVideoPlayer == null) return;

        try {
            if (isPlaying) {
                gsyVideoPlayer.onVideoPause();
                isPlaying = false;
                progressHandler.removeCallbacks(progressRunnable);
            } else {
                gsyVideoPlayer.onVideoResume();
                isPlaying = true;
                progressHandler.post(progressRunnable);
            }

            if (listener != null) {
                listener.onPlayStateChanged(isPlaying);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error toggling play/pause", e);
        }
    }
    
    public void playNext() {
        if (audioList == null || audioList.isEmpty()) return;

        // 检查是否已经是最后一个曲子
        if (currentIndex == audioList.size() - 1) {
            // 如果是最后一个，先尝试加载更多
            if (listener != null) {
                listener.onNeedLoadMore(new OnLoadMoreCallback() {
                    @Override
                    public void onLoadMoreComplete(boolean hasMoreData) {
                        if (hasMoreData) {
                            // 有更多数据，切换到下一曲
                            currentIndex = currentIndex + 1;
                            // 确保索引不超出范围
                            if (currentIndex >= audioList.size()) {
                                currentIndex = 0;
                            }
                            playCurrentAudio();
                        } else {
                            // 没有更多数据，提示用户
                            if (listener != null) {
                                listener.onError("已经是最后一个了，无法切换下一曲");
                            }
                        }
                    }
                });
            }
        } else {
            // 不是最后一个，直接切换
            currentIndex = (currentIndex + 1) % audioList.size();
            playCurrentAudio();
        }
    }
    
    public void playPrevious() {
        if (audioList == null || audioList.isEmpty()) return;
        
        currentIndex = (currentIndex - 1 + audioList.size()) % audioList.size();
        playCurrentAudio();
    }
    
    public void seekTo(int position) {
        if (gsyVideoPlayer != null) {
            try {
                gsyVideoPlayer.seekTo(position);
            } catch (Exception e) {
                Log.e(TAG, "Error seeking", e);
            }
        }
    }
    
    public void setOnAudioPlayerListener(OnAudioPlayerListener listener) {
        this.listener = listener;
    }
    
    public boolean isPlaying() {
        return isPlaying;
    }
    
    public GAudioVo getCurrentAudio() {
        if (audioList != null && currentIndex < audioList.size()) {
            return audioList.get(currentIndex);
        }
        return null;
    }
    
    public void release() {
        if (gsyVideoPlayer != null) {
            gsyVideoPlayer.release();
        }
        if (progressHandler != null) {
            progressHandler.removeCallbacks(progressRunnable);
        }
        isPlaying = false;
    }
}
